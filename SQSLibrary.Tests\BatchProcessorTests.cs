using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using SQSLibrary;
using SQSLibrary.Services;

namespace SQSLibrary.Tests
{
    /// <summary>
    /// Unit tests for BatchProcessor functionality
    /// </summary>
    public class BatchProcessorTests
    {
        [Fact]
        public void Constructor_ValidParameters_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var batchProcessor = new BatchProcessor(10, 5000);

            // Assert
            batchProcessor.Should().NotBeNull();
            batchProcessor.IsBatchReady().Should().BeFalse();
        }

        [Fact]
        public void AddS3DownloadResults_ValidResults_ShouldParseAndAddEvents()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var downloadResults = CreateTestDownloadResults(3);

            // Act
            var parsedEvents = batchProcessor.AddS3DownloadResults(downloadResults);

            // Assert
            parsedEvents.Should().HaveCount(3);
            parsedEvents.All(e => e != null).Should().BeTrue();
            parsedEvents.All(e => !string.IsNullOrEmpty(e.CallIdentifier)).Should().BeTrue();
        }

        [Fact]
        public void AddS3DownloadResults_FailedDownloads_ShouldSkipFailedResults()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var downloadResults = new List<S3DownloadResult>
            {
                CreateSuccessfulDownloadResult("CALL_001", "StartCall"),
                CreateFailedDownloadResult("Failed to download"),
                CreateSuccessfulDownloadResult("CALL_002", "EndCall")
            };

            // Act
            var parsedEvents = batchProcessor.AddS3DownloadResults(downloadResults);

            // Assert
            parsedEvents.Should().HaveCount(2);
            parsedEvents[0].CallIdentifier.Should().Be("CALL_001");
            parsedEvents[1].CallIdentifier.Should().Be("CALL_002");
        }

        [Fact]
        public void IsBatchReady_BatchSizeReached_ShouldReturnTrue()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(2, 10000); // Small batch size
            var downloadResults = CreateTestDownloadResults(2);

            // Act
            batchProcessor.AddS3DownloadResults(downloadResults);

            // Assert
            batchProcessor.IsBatchReady().Should().BeTrue();
        }

        [Fact]
        public void IsBatchReady_EmptyBatch_ShouldReturnFalse()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);

            // Act & Assert
            batchProcessor.IsBatchReady().Should().BeFalse();
        }

        [Fact]
        public void GetOrderedBatch_MultipleEvents_ShouldReturnOrderedByTimestamp()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var baseTime = DateTime.UtcNow;
            
            var downloadResults = new List<S3DownloadResult>
            {
                CreateSuccessfulDownloadResult("CALL_001", "StartCall", baseTime.AddSeconds(30)),
                CreateSuccessfulDownloadResult("CALL_001", "Media", baseTime.AddSeconds(10)),
                CreateSuccessfulDownloadResult("CALL_001", "EndCall", baseTime.AddSeconds(60))
            };

            // Act
            batchProcessor.AddS3DownloadResults(downloadResults);
            var orderedEvents = batchProcessor.GetOrderedBatch();

            // Assert
            orderedEvents.Should().HaveCount(3);
            orderedEvents[0].EventType.Should().Be("Media");     // Earliest timestamp
            orderedEvents[1].EventType.Should().Be("StartCall"); // Middle timestamp
            orderedEvents[2].EventType.Should().Be("EndCall");   // Latest timestamp
        }

        [Fact]
        public void GetOrderedBatch_SameTimestamp_ShouldOrderByCallIdentifierOnly()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var sameTime = DateTime.UtcNow;

            var downloadResults = new List<S3DownloadResult>
            {
                CreateSuccessfulDownloadResult("CALL_002", "StartCall", sameTime),
                CreateSuccessfulDownloadResult("CALL_001", "StartCall", sameTime),
                CreateSuccessfulDownloadResult("CALL_001", "EndCall", sameTime)
            };

            // Act
            batchProcessor.AddS3DownloadResults(downloadResults);
            var orderedEvents = batchProcessor.GetOrderedBatch();

            // Assert
            orderedEvents.Should().HaveCount(3);
            // Should be ordered by CallIdentifier ASC only (EventType order is not guaranteed)
            orderedEvents[0].CallIdentifier.Should().Be("CALL_001");
            orderedEvents[1].CallIdentifier.Should().Be("CALL_001");
            orderedEvents[2].CallIdentifier.Should().Be("CALL_002");
            orderedEvents[2].EventType.Should().Be("StartCall");

            // Verify that both CALL_001 events are present (order between them is not guaranteed)
            var call001Events = orderedEvents.Where(e => e.CallIdentifier == "CALL_001").ToList();
            call001Events.Should().HaveCount(2);
            call001Events.Should().Contain(e => e.EventType == "StartCall");
            call001Events.Should().Contain(e => e.EventType == "EndCall");
        }

        [Fact]
        public void GetOrderedBatch_AfterGettingBatch_ShouldClearBatch()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var downloadResults = CreateTestDownloadResults(3);

            // Act
            batchProcessor.AddS3DownloadResults(downloadResults);
            var firstBatch = batchProcessor.GetOrderedBatch();
            var secondBatch = batchProcessor.GetOrderedBatch();

            // Assert
            firstBatch.Should().HaveCount(3);
            secondBatch.Should().BeEmpty();
            batchProcessor.IsBatchReady().Should().BeFalse();
        }

        [Fact]
        public void GetOrderedBatch_EmptyBatch_ShouldReturnEmptyList()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);

            // Act
            var orderedEvents = batchProcessor.GetOrderedBatch();

            // Assert
            orderedEvents.Should().NotBeNull();
            orderedEvents.Should().BeEmpty();
        }

        [Fact]
        public void GetOrderedBatch_ComplexSorting_ShouldOrderCorrectly()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var baseTime = DateTime.UtcNow;

            var downloadResults = new List<S3DownloadResult>
            {
                // Same timestamp, different calls and events
                CreateSuccessfulDownloadResult("CALL_003", "Media", baseTime),
                CreateSuccessfulDownloadResult("CALL_001", "StartCall", baseTime),
                CreateSuccessfulDownloadResult("CALL_002", "EndCall", baseTime),
                CreateSuccessfulDownloadResult("CALL_001", "EndCall", baseTime),
                CreateSuccessfulDownloadResult("CALL_001", "Media", baseTime),
                // Different timestamp
                CreateSuccessfulDownloadResult("CALL_001", "StartCall", baseTime.AddSeconds(-10)),
                CreateSuccessfulDownloadResult("CALL_002", "StartCall", baseTime.AddSeconds(10))
            };

            // Act
            batchProcessor.AddS3DownloadResults(downloadResults);
            var orderedEvents = batchProcessor.GetOrderedBatch();

            // Assert
            orderedEvents.Should().HaveCount(7);

            // First by timestamp (earliest first) - use BeCloseTo for timestamp comparison due to precision
            orderedEvents[0].Timestamp.Should().BeCloseTo(baseTime.AddSeconds(-10), TimeSpan.FromMilliseconds(1));
            orderedEvents[0].CallIdentifier.Should().Be("CALL_001");
            orderedEvents[0].EventType.Should().Be("StartCall");

            // Then same timestamp, ordered by call identifier only (no event type sorting)
            orderedEvents[1].Timestamp.Should().BeCloseTo(baseTime, TimeSpan.FromMilliseconds(1));
            orderedEvents[1].CallIdentifier.Should().Be("CALL_001");
            // Note: Event type order is not guaranteed since we only sort by timestamp and call identifier

            orderedEvents[2].CallIdentifier.Should().Be("CALL_001");
            orderedEvents[3].CallIdentifier.Should().Be("CALL_001");
            orderedEvents[4].CallIdentifier.Should().Be("CALL_002");
            orderedEvents[5].CallIdentifier.Should().Be("CALL_003");

            // Latest timestamp
            orderedEvents[6].Timestamp.Should().BeCloseTo(baseTime.AddSeconds(10), TimeSpan.FromMilliseconds(1));
            orderedEvents[6].CallIdentifier.Should().Be("CALL_002");
            orderedEvents[6].EventType.Should().Be("StartCall");
        }

        [Fact]
        public async Task IsBatchReady_TimeoutReached_ShouldReturnTrue()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(100, 100); // Very short timeout
            var downloadResults = CreateTestDownloadResults(1);

            // Act
            batchProcessor.AddS3DownloadResults(downloadResults);

            // Wait for timeout using async delay
            await Task.Delay(150);

            // Assert
            batchProcessor.IsBatchReady().Should().BeTrue();
        }

        [Fact]
        public void AddS3DownloadResults_NullResults_ShouldHandleGracefully()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);

            // Act
            var parsedEvents = batchProcessor.AddS3DownloadResults(null);

            // Assert
            parsedEvents.Should().NotBeNull();
            parsedEvents.Should().BeEmpty();
            batchProcessor.IsBatchReady().Should().BeFalse();
        }

        [Fact]
        public void AddS3DownloadResults_EmptyResults_ShouldHandleGracefully()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(10, 5000);
            var emptyResults = new List<S3DownloadResult>();

            // Act
            var parsedEvents = batchProcessor.AddS3DownloadResults(emptyResults);

            // Assert
            parsedEvents.Should().NotBeNull();
            parsedEvents.Should().BeEmpty();
            batchProcessor.IsBatchReady().Should().BeFalse();
        }

        private static List<S3DownloadResult> CreateTestDownloadResults(int count)
        {
            var results = new List<S3DownloadResult>();
            var baseTime = DateTime.UtcNow;

            for (int i = 0; i < count; i++)
            {
                var timestamp = baseTime.AddSeconds(i * 10);
                var callId = $"CALL_{i:D3}";
                var eventType = GetEventType(i % 4);

                results.Add(CreateSuccessfulDownloadResult(callId, eventType, timestamp));
            }

            return results;
        }

        private static S3DownloadResult CreateSuccessfulDownloadResult(string callId, string eventType, DateTime? timestamp = null)
        {
            var actualTimestamp = timestamp ?? DateTime.UtcNow;
            var xmlContent = CreateSampleLogEventXml(actualTimestamp, callId, eventType);

            return new S3DownloadResult
            {
                S3Object = new S3ObjectInfo
                {
                    BucketName = "test-bucket",
                    ObjectKey = $"test-events/{callId}_{eventType}.xml",
                    Size = xmlContent.Length,
                    EventTime = actualTimestamp,
                    EventName = "ObjectCreated:Put",
                    Region = "us-east-1",
                    SqsReceiptHandle = "test-receipt-handle",
                    SqsMessageId = "test-message-id"
                },
                XmlContent = xmlContent,
                IsSuccess = true,
                DownloadTime = DateTime.UtcNow
            };
        }

        private static S3DownloadResult CreateFailedDownloadResult(string errorMessage)
        {
            return new S3DownloadResult
            {
                S3Object = new S3ObjectInfo
                {
                    BucketName = "test-bucket",
                    ObjectKey = "failed-object.xml",
                    Size = 1024,
                    EventTime = DateTime.UtcNow,
                    EventName = "ObjectCreated:Put",
                    Region = "us-east-1"
                },
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }

        private static string GetEventType(int sequence)
        {
            var eventTypes = new[] { "StartCall", "Media", "EndMedia", "EndCall" };
            return eventTypes[sequence % eventTypes.Length];
        }

        private static string CreateSampleLogEventXml(DateTime timestamp, string callId, string eventType)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
                    <LogEvent xmlns=""http://solacom.com/Logging"">
                        <timestamp>{timestamp:yyyy-MM-ddTHH:mm:ss.fffZ}</timestamp>
                        <agencyOrElement>TestAgency</agencyOrElement>
                        <agent>Agent001</agent>
                        <callIdentifier>{callId}</callIdentifier>
                        <incidentIdentifier>_II_{callId}</incidentIdentifier>
                        <eventType>{eventType}</eventType>
                    </LogEvent>";
        }
    }
}
