using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SQSConsumerService.Services;
using SQSLibrary;
using SQSLibrary.Configuration;
using Xunit;

namespace SQSConsumerService.IntegrationTests
{
    /// <summary>
    /// Integration tests for automatic configuration refresh functionality
    /// </summary>
    public class AutomaticConfigurationRefreshIntegrationTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;

        public AutomaticConfigurationRefreshIntegrationTests()
        {
            var services = new ServiceCollection();
            
            // Setup configuration
            var configBuilder = new ConfigurationBuilder();
            var initialConfig = new Dictionary<string, string>
            {
                ["configurationChangeMonitor:enabled"] = "true",
                ["configurationChangeMonitor:debounceDelayMs"] = "100",
                ["ClientQueues:0:ClientCode"] = "TEST001",
                ["ClientQueues:0:QueueName"] = "test-queue-1",
                ["ClientQueues:0:QueueUrl"] = "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue-1",
                ["ClientQueues:0:Region"] = "us-east-1"
            };
            
            configBuilder.AddInMemoryCollection(initialConfig);
            _configuration = configBuilder.Build();

            // Register services
            services.AddSingleton(_configuration);
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
            services.AddSingleton<IConfigurationChangeNotificationService, ConfigurationChangeNotificationService>();
            
            // Register configuration change monitor settings
            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var settings = new ConfigurationChangeMonitorSettings();
                config.Bind("configurationChangeMonitor", settings);
                return settings;
            });

            // Register configuration objects
            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var awsSettings = new AwsSettings();
                config.Bind("awsSettings", awsSettings);
                return awsSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var sqsSettings = new SQSSettings();
                config.Bind("sqsSettings", sqsSettings);
                return sqsSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var s3Settings = new S3Settings();
                config.Bind("s3Settings", s3Settings);
                return s3Settings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var batchSettings = new BatchProcessingSettings();
                config.Bind("batchProcessingSettings", batchSettings);
                return batchSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var healthSettings = new HealthCheckSettings();
                config.Bind("healthCheckSettings", healthSettings);
                return healthSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var collectorSettings = new CollectorAPISettings();
                config.Bind("collectorAPISettings", collectorSettings);
                return collectorSettings;
            });

            // Register hosted service
            services.AddHostedService<ConfigurationChangeMonitor>();

            _serviceProvider = services.BuildServiceProvider();
        }

        [Fact]
        public void ConfigurationChangeNotificationService_ShouldBeRegistered()
        {
            // Act
            var notificationService = _serviceProvider.GetService<IConfigurationChangeNotificationService>();

            // Assert
            Assert.NotNull(notificationService);
            Assert.IsType<ConfigurationChangeNotificationService>(notificationService);
        }

        [Fact]
        public void ConfigurationChangeMonitor_ShouldBeRegisteredAsHostedService()
        {
            // Act
            var hostedServices = _serviceProvider.GetServices<IHostedService>();

            // Assert
            Assert.Contains(hostedServices, service => service is ConfigurationChangeMonitor);
        }

        [Fact]
        public void ConfigurationChangeMonitorSettings_ShouldBeConfiguredCorrectly()
        {
            // Act
            var settings = _serviceProvider.GetRequiredService<ConfigurationChangeMonitorSettings>();

            // Assert
            Assert.NotNull(settings);
            Assert.True(settings.Enabled);
            Assert.Equal(100, settings.DebounceDelayMs);
        }

        [Fact]
        public async Task ConfigurationChangeNotificationService_ShouldPublishAndReceiveEvents()
        {
            // Arrange
            var notificationService = _serviceProvider.GetRequiredService<IConfigurationChangeNotificationService>();
            var eventReceived = false;
            ConfigurationChangedEventArgs receivedArgs = null;

            void Handler(object sender, ConfigurationChangedEventArgs e)
            {
                eventReceived = true;
                receivedArgs = e;
            }

            notificationService.Subscribe(Handler);

            // Act
            await notificationService.PublishConfigurationChangedAsync("Test configuration change");

            // Assert
            Assert.True(eventReceived);
            Assert.NotNull(receivedArgs);
            Assert.Equal("Test configuration change", receivedArgs.Details);
        }

        [Fact]
        public async Task ConfigurationChangeMonitor_ShouldStartAndStopSuccessfully()
        {
            // Arrange
            var monitor = _serviceProvider.GetServices<IHostedService>()
                .OfType<ConfigurationChangeMonitor>()
                .FirstOrDefault();

            Assert.NotNull(monitor);

            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(500));

            // Act & Assert - Should not throw
            await monitor.StartAsync(cts.Token);
            await monitor.StopAsync(CancellationToken.None);
        }

        [Fact]
        public void Configuration_ShouldContainExpectedClientQueues()
        {
            // Act
            var queues = new List<QueueDefinition>();
            _configuration.Bind("ClientQueues", queues);

            // Assert
            Assert.Single(queues);
            Assert.Equal("TEST001", queues[0].ClientCode);
            Assert.Equal("test-queue-1", queues[0].QueueName);
            Assert.Equal("https://sqs.us-east-1.amazonaws.com/123456789012/test-queue-1", queues[0].QueueUrl);
            Assert.Equal("us-east-1", queues[0].Region);
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }

    /// <summary>
    /// Tests for configuration change detection logic
    /// </summary>
    public class ConfigurationChangeDetectionTests
    {
        [Fact]
        public void DetectQueueConfigurationChanges_WithIdenticalQueues_ShouldReturnFalse()
        {
            // Arrange
            var queue1 = new List<QueueDefinition>
            {
                new QueueDefinition { ClientCode = "TEST001", QueueName = "queue1", QueueUrl = "url1", Region = "us-east-1" }
            };
            var queue2 = new List<QueueDefinition>
            {
                new QueueDefinition { ClientCode = "TEST001", QueueName = "queue1", QueueUrl = "url1", Region = "us-east-1" }
            };

            // Act
            var hasChanges = DetectQueueConfigurationChanges(queue1, queue2);

            // Assert
            Assert.False(hasChanges);
        }

        [Fact]
        public void DetectQueueConfigurationChanges_WithDifferentQueueCounts_ShouldReturnTrue()
        {
            // Arrange
            var queue1 = new List<QueueDefinition>
            {
                new QueueDefinition { ClientCode = "TEST001", QueueName = "queue1", QueueUrl = "url1", Region = "us-east-1" }
            };
            var queue2 = new List<QueueDefinition>
            {
                new QueueDefinition { ClientCode = "TEST001", QueueName = "queue1", QueueUrl = "url1", Region = "us-east-1" },
                new QueueDefinition { ClientCode = "TEST002", QueueName = "queue2", QueueUrl = "url2", Region = "us-east-1" }
            };

            // Act
            var hasChanges = DetectQueueConfigurationChanges(queue1, queue2);

            // Assert
            Assert.True(hasChanges);
        }

        [Fact]
        public void DetectQueueConfigurationChanges_WithModifiedQueue_ShouldReturnTrue()
        {
            // Arrange
            var queue1 = new List<QueueDefinition>
            {
                new QueueDefinition { ClientCode = "TEST001", QueueName = "queue1", QueueUrl = "url1", Region = "us-east-1" }
            };
            var queue2 = new List<QueueDefinition>
            {
                new QueueDefinition { ClientCode = "TEST001", QueueName = "queue1-modified", QueueUrl = "url1", Region = "us-east-1" }
            };

            // Act
            var hasChanges = DetectQueueConfigurationChanges(queue1, queue2);

            // Assert
            Assert.True(hasChanges);
        }

        private static bool DetectQueueConfigurationChanges(List<QueueDefinition> previousQueues, List<QueueDefinition> currentQueues)
        {
            if (previousQueues == null || currentQueues == null)
                return true;

            if (previousQueues.Count != currentQueues.Count)
                return true;

            foreach (var previous in previousQueues)
            {
                var current = currentQueues.FirstOrDefault(q => q.ClientCode == previous.ClientCode);
                if (current == null)
                    return true;

                if (previous.QueueName != current.QueueName ||
                    previous.QueueUrl != current.QueueUrl ||
                    previous.Region != current.Region)
                    return true;
            }

            return currentQueues.Any(current => !previousQueues.Any(q => q.ClientCode == current.ClientCode));
        }
    }
}
