using System;

namespace SQSLibrary.Configuration
{
    /// <summary>
    /// Collector API configuration settings
    /// </summary>
    public class CollectorAPISettings
    {
        /// <summary>
        /// Base URL for the collector API (e.g., "http://localhost:62080")
        /// </summary>
        public string BaseUrl { get; set; } = "";

        /// <summary>
        /// Endpoint path for sending events (e.g., "/api/events")
        /// </summary>
        public string EventsEndpoint { get; set; } = "/api/events";

        /// <summary>
        /// Endpoint path for health checks (e.g., "/api/health")
        /// </summary>
        public string HealthEndpoint { get; set; } = "/api/health/ping";

        /// <summary>
        /// User ID for API authentication
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// API Key for authentication (encrypted)
        /// </summary>
        public string ApiKey { get; set; } = "";

        /// <summary>
        /// Timeout for API requests in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Gets the full URL for sending events
        /// </summary>
        public string GetEventsUrl()
        {
            return $"{BaseUrl.TrimEnd('/')}{EventsEndpoint}";
        }

        /// <summary>
        /// Gets the full URL for health checks
        /// </summary>
        public string GetHealthUrl()
        {
            return $"{BaseUrl.TrimEnd('/')}{HealthEndpoint}";
        }

        /// <summary>
        /// Gets the full URL for a specific client's events endpoint
        /// </summary>
        /// <param name="clientCode">Client code to insert into the URL</param>
        /// <returns>Full URL with client code</returns>
        public string GetClientEventsUrl(string clientCode)
        {
            var endpoint = EventsEndpoint.Replace("{clientCode}", clientCode ?? "");
            return $"{BaseUrl.TrimEnd('/')}{endpoint}".TrimEnd('/') + "/";
        }

        /// <summary>
        /// Gets the decrypted user ID for API authentication
        /// </summary>
        /// <returns>Decrypted user ID</returns>
        public string GetDecryptedUserId()
        {
            if (string.IsNullOrEmpty(UserId))
                return string.Empty;

            try
            {
                return Encryption.Decrypt(UserId);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to decrypt userId");
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the decrypted API key for authentication
        /// </summary>
        /// <returns>Decrypted API key</returns>
        public string GetDecryptedApiKey()
        {
            if (string.IsNullOrEmpty(ApiKey))
                return string.Empty;

            try
            {
                return Encryption.Decrypt(ApiKey);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to decrypt apiKey");
                return string.Empty;
            }
        }
    }
}
