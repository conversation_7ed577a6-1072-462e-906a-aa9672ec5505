﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.3.2" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="3.1.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="3.1.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.400.44" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SQSLibrary\SQSLibrary.csproj" />
    <ProjectReference Include="..\SQSConsumerService\SQSConsumerService.csproj" />
  </ItemGroup>

</Project>
