# SQS Consumer Service

A .NET Core service that consumes S3 event notifications from Amazon SQS queues, downloads LogEvent XML files from S3, and processes them with batch ordering.

## Architecture Overview

The service implements a cloud-native architecture using AWS services for LogEvent processing:

### High-Level Flow
![alt text](./docs/img/high-level-flow.png)
```
graph TB
    subgraph "AWS Cloud"
        S3[S3 Buckets<br/>LogEvent XML Files]
        SNS[SNS Topics<br/>Event Notifications]
        SQS[SQS Queues<br/>Message Buffer]
        SM[Secrets Manager<br/>Configuration]
    end

    subgraph "SQS Consumer Service"
        QO[Queue Orchestrator<br/>Centralized Management]
        HC[Health Checker<br/>Circuit Breaker]
        SC[SQS Consumers<br/>Message Polling]
        S3D[S3 File Downloader<br/>Parallel Downloads]
        BP[Batch Processor<br/>Event Ordering]
    end

    subgraph "External Systems"
        CA[Collector API<br/>Event Processing]
    end

    S3 -->|Event Notification| SNS
    SNS -->|Forward Event| SQS
    SM -->|Configuration| QO
    QO -->|Manages| SC
    QO -->|Initializes| HC
    SC -->|Polls Messages| SQS
    SC -->|Downloads Files| S3
    SC -->|Uses| S3D
    SC -->|Processes Batches| BP
    HC -->|Monitors| CA
    BP -->|Sends Events| CA

    style QO fill:#FFECB3,stroke:#2196F3,stroke-width:2px,color:#212121
    style HC fill:#FFECB3,stroke:#2196F3,stroke-width:2px,color:#212121
    style BP fill:#FFECB3,stroke:#2196F3,stroke-width:2px,color:#212121
    style S3D fill:#FFECB3,stroke:#2196F3,stroke-width:2px,color:#212121
```

### Key Features
- **S3-Based Architecture**: LogEvent XML files stored in S3 with event notifications
- **Queue Orchestrator Pattern**: Centralized queue management with lifecycle control and automatic configuration refresh
- **Automatic Configuration Refresh**: Kralizek AWS Secrets Manager integration with auto-polling and pub-sub event notifications for configuration updates
- **Batch Processing**: Events ordered by timestamp, call identifier, supports events without call IDs (e.g., agent events)
- **Health-Based Flow Control**: Automatic pause/resume based on collector API health with secure endpoint masking
- **Parallel S3 Downloads**: Configurable concurrent downloads for optimal performance
- **Fault Tolerance**: Retry logic, error handling, and graceful degradation
- **LocalStack Support**: Complete local development environment
- **Pub-Sub Architecture**: Event-driven configuration change notifications with automatic consumer restart

## Project Structure

This solution consists of:
1. **SQSConsumerService** - The main consumer service that processes S3-based SQS messages
2. **SQSLibrary** - Core library containing SQS, S3, and batch processing functionality
3. **SQSLibrary.Tests** - Unit tests for the core library (56 tests)
4. **SQSConsumerService.Tests** - Queue orchestrator unit tests (29 tests)
5. **SQSConsumerService.IntegrationTests** - Integration tests for end-to-end scenarios (20 tests)

## Prerequisites
- .NET Core 3.1 SDK
- AWS CLI and LocalStack (for local development)
- Docker (optional, for containerized deployment)

# Build and Test

## Building the Solution
```bash
dotnet build SQSConsumerService.sln
```

## Running Tests

### Unit Tests (No Dependencies Required)
```bash
# Run core library tests (56 tests, isolated - no external dependencies)
dotnet test SQSLibrary.Tests

# Run service tests (29 tests, queue orchestrator - no external dependencies)
dotnet test SQSConsumerService.Tests
```

### Integration Tests (⚠️ **REQUIRES LOCALSTACK**)
```bash
# IMPORTANT: Integration tests require LocalStack to be running
# Start LocalStack first:
start-localstack.bat
# OR manually:
docker-compose -f docker-compose.local.yml up localstack -d

# Then run integration tests (20 tests)
dotnet test SQSConsumerService.IntegrationTests

# Run all tests (105 tests total) - REQUIRES LOCALSTACK
dotnet test SQSConsumerService.sln
```

### Test Dependencies Summary
- **Unit Tests**: ✅ No external dependencies required
- **Integration Tests**: ⚠️ **REQUIRES LocalStack running on localhost:4566**
- **LocalStack Services**: S3, SQS, SNS, IAM must be available

## Running the Consumer Service
```bash
dotnet run --project SQSConsumerService
```

## Quick Start with LocalStack (S3-Based Architecture)

### One-Command Setup
```bash
# Complete setup: Start LocalStack + Create Infrastructure
start-localstack.bat
```

### Manual Step-by-Step
```bash
# Start LocalStack and SQS Consumer Service
# Infrastructure is created automatically when LocalStack starts
docker-compose -f docker-compose.local.yml up --build -d
```

### Test the Implementation
```bash
# Run unit tests
dotnet test SQSLibrary.Tests

# Run integration tests (requires LocalStack running)
dotnet test SQSConsumerService.IntegrationTests

# Generate test data in LocalStack
scripts\generate-test-data.bat
```

### Run the SQS Consumer Service
```bash
# Run the consumer to process S3-based LogEvents
dotnet run --project SQSConsumerService/SQSConsumerService.csproj
```

### Stop LocalStack
```bash
# Stop LocalStack when done
docker-compose -f docker-compose-localstack.yml down
```

## Environment Configuration

The service supports environment-based configuration using `.env` files and environment variables.

### Environment Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file for your environment:**
   ```bash
   # Set your target environment
   ASPNETCORE_ENVIRONMENT=Local  # Options: Local, Development, QA, Production, Production-ca

   # Configure other settings as needed
   HTTP_PORT=8080
   BATCH_SIZE=5
   LOG_LEVEL_DEFAULT=Debug
   ```

### Available Environments

| Environment | Description | Use Case |
|-------------|-------------|----------|
| `Local` | LocalStack development | Local development with mock AWS services |
| `Development` | AWS development environment | Development with real AWS services |
| `QA` | QA environment | Testing with QA AWS resources |
| `Production` | Production US environment | Production deployment in US region |
| `Production-ca` | Production Canada environment | Production deployment in Canada region |

### Environment Variables

The service supports comprehensive environment variable configuration. Key variables include:

- **`ASPNETCORE_ENVIRONMENT`**: Determines which appsettings file to load
- **`AWS_REGION`**: AWS region for services
- **`BATCH_SIZE`**: Number of events to batch before sending
- **`LOG_LEVEL_DEFAULT`**: Logging level (Debug, Information, Warning, Error)
- **`HTTP_PORT`**: Host port mapping for the service

See `.env.example` for a complete list of configurable variables.

## Docker Deployment (Recommended)

The service supports containerized deployment with Docker and Docker Compose with environment-specific configurations.

### Quick Start with Docker

#### Local Development with LocalStack
```bash
# 1. Set up environment
cp .env.example .env
# Edit .env file: set ASPNETCORE_ENVIRONMENT=Local

# 2. Start complete local environment with LocalStack
docker-compose -f docker-compose.local.yml up --build -d

# 3. Check service health
curl http://localhost:8080/api/health

# 4. View logs
docker-compose -f docker-compose.local.yml logs -f

# 5. Stop services
docker-compose -f docker-compose.local.yml down
```

#### Development/QA/Production Environments
```bash
# 1. Configure for your target environment
cp .env.example .env
# Edit .env: set ASPNETCORE_ENVIRONMENT=Development (or QA, Production, etc.)

# 2. Start the service
docker-compose up --build -d

# 3. Check service health
curl http://localhost:8080/api/health
```

### Docker Compose Structure

| File | Purpose | Usage |
|------|---------|-------|
| `docker-compose.yml` | Base configuration for all environments | Used for dev, qa, prod with real AWS services |
| `docker-compose.local.yml` | LocalStack development | Standalone for local development with mock AWS |
| `docker-compose.override.yml` | Development defaults | Automatically loaded by docker-compose for local dev |

### Runtime Configuration Override

You can override any configuration at runtime using environment variables:

```bash
# Override batch size for a specific run
BATCH_SIZE=10 docker-compose up

# Override multiple settings for QA
ASPNETCORE_ENVIRONMENT=QA BATCH_SIZE=20 LOG_LEVEL_DEFAULT=Information docker-compose up

# Override for production deployment
ASPNETCORE_ENVIRONMENT=Production \
BATCH_SIZE=1 \
LOG_LEVEL_DEFAULT=Warning \
HEALTH_CHECK_INTERVAL_SECONDS=60 \
docker-compose up -d
```

### Environment Variable Priority

Configuration values are resolved in the following order (highest to lowest priority):

1. **Runtime Environment Variables** (e.g., `BATCH_SIZE=10 docker-compose up`)
2. **`.env` file** (project-specific environment variables)
3. **Docker Compose environment section** (defaults in docker-compose files)
4. **appsettings.{Environment}.json** (environment-specific configuration files)
5. **appsettings.json** (base configuration file)

### Testing Environment Configuration

```bash
# Test local environment with LocalStack
docker-compose -f docker-compose.local.yml up --build

# Test development environment with real AWS
ASPNETCORE_ENVIRONMENT=Development docker-compose up --build

# Test with custom settings
BATCH_SIZE=1 LOG_LEVEL_DEFAULT=Debug docker-compose up --build

# Verify configuration is loaded correctly
curl http://localhost:8080/api/health/info
```

## Architecture Deep Dive

### Design Decisions

#### 1. Queue Orchestrator Pattern
**Decision**: Implement a centralized queue orchestrator to manage all SQS consumers and health checking.

**Implementation**:
- **Centralized Management**: Single point of control for all queue operations
- **Lifecycle Management**: Handles initialization, startup, shutdown, and cleanup
- **Health Monitoring**: Integrates with shared health checker for consolidated status
- **Configuration Validation**: Validates queue configurations before startup
- **Error Handling**:  Error handling with proper logging and recovery

**Rationale**:
- **Maintainability**: Centralizes queue management logic in a single, testable component
- **Scalability**: Easier to add new queues and manage multiple consumers
- **Observability**: Provides unified health status and detailed consumer information
- **Reliability**: Better error handling and recovery mechanisms
- **Testability**: Isolated orchestrator logic enables detailed unit and integration testing
- **Performance**: Configuration loaded once at startup and injected, reducing redundant parsing

#### 2. Batch Processing
**Decision**: Implement timestamp and call identifier-based ordering with configurable batch sizes.

**Sorting Logic**: Events are sorted by:
1. **Timestamp** (ascending) - Chronological order
2. **Call Identifier** (ascending) - Consistent call grouping

**Rationale**:
- **Ordering**: Ensures chronological processing with call grouping
- **Reliability**: Batch failures don't affect individual event processing

#### 2. Health-Based Flow Control
**Decision**: Monitor collector API health and pause consumption when unhealthy.

**Implementation**:
- **Shared Health Checker**: Single health checker instance across all SQS consumers eliminates duplicate health checks
- **Periodic Health Checks**: Configurable interval (default: 30 seconds)
- **Circuit Breaker Pattern**: Mark unhealthy after N consecutive failures
- **Non-Blocking Design**: Health checks don't block message processing
- **Graceful Recovery**: Automatic resume when health is restored

**Rationale**:
- **Reliability**: Prevents message loss during collector outages
- **Resource Protection**: Avoids overwhelming unhealthy downstream services
- **Automatic Recovery**: Resumes processing when health is restored
- **Operational Excellence**: Reduces manual intervention during incidents
- **Efficiency**: Shared health checker reduces redundant API calls and log noise

**Health Check Flow**:
![alt text](./docs/img/health-check-flow.png)
```
graph TD
    A[Shared Health Checker<br/>Timer: 30s interval] --> B[HTTP GET<br/>collector-api/health]
    B --> C{Response<br/>Status?}

    C -->|200 OK| D[Mark Healthy<br/>Reset Failure Count]
    C -->|4xx/5xx Error| E[Increment Failure Count]
    C -->|Network Error| E
    C -->|Timeout| E

    E --> F{Consecutive Failures<br/>≥ Threshold?}
    F -->|No| G[Continue Processing<br/>Log Warning]
    F -->|Yes| H[Mark Unhealthy<br/>Circuit Breaker Open]

    H --> I[Pause All SQS Consumers<br/>Stop Message Processing]
    I --> J[Wait for Recovery<br/>Continue Health Checks]
    J --> B

    D --> K[Resume Processing<br/>All Consumers Active]
    G --> K
    K --> L[Wait for Next<br/>Health Check Interval]
    L --> A

    style A fill:#1976D2,stroke:#1976D2,stroke-width:2px
    style H fill:#D32F2F,stroke:#D32F2F,stroke-width:2px
    style D fill:#388E3C,stroke:#388E3C,stroke-width:2px
    style I fill:#F57C00,stroke:#F57C00,stroke-width:2px
```

#### 3. Parallel S3 Downloads
**Decision**: Implement configurable concurrent downloads with semaphore-based throttling.

**Rationale**:
- **Performance**: Dramatically improves throughput for high-volume scenarios
- **Resource Management**: Prevents overwhelming S3 or local resources
- **Configurability**: Allows tuning based on environment capabilities
- **Fault Tolerance**: Individual download failures don't block the entire batch

### Message Flow Architecture
![alt text](./docs/img/sequence-diagram.png)
```
sequenceDiagram
    participant App as External Application
    participant S3 as S3 Bucket
    participant SNS as SNS Topic
    participant SQS as SQS Queue
    participant QO as Queue Orchestrator
    participant SC as SQS Consumer
    participant HC as Health Checker
    participant S3D as S3 File Downloader
    participant BP as Batch Processor
    participant CA as Collector API
    Note over QO: Service Startup
    QO->>QO: Initialize Configuration
    QO->>HC: Create Shared Health Checker
    QO->>SC: Create SQS Managers per Queue
    QO->>HC: Start Health Monitoring
    Note over App,S3:Received Event
    App->>S3: Upload LogEvent.xml
    S3->>SNS: S3 Event Notification
    SNS->>SQS: Forward to Queue(s)
    Note over HC,CA: Continuous Health Monitoring
    loop Every 30 seconds
        HC->>CA: GET /health
        CA->>HC: 200 OK / Error Response
        HC->>HC: Update Circuit Breaker State
    end
    Note over SC,CA: Message Processing Loop
    loop Continuous Processing
        alt API Healthy (Circuit Closed)
            SC->>SQS: Long Poll Messages (20s)
            SQS->>SC: S3 Event Notifications (batch)
            SC->>SC: Parse S3 Event Notifications
            par Parallel S3 Downloads
                SC->>S3D: Download LogEvent 1
                SC->>S3D: Download LogEvent 2
                SC->>S3D: Download LogEvent N
            end
            S3D->>SC: XML Content Results
            SC->>BP: Add Events to Batch
            alt Batch Ready (size/timeout)
                BP->>BP: Sort by Timestamp + CallId
                BP->>CA: POST /events (ordered)
                CA->>BP: 200 OK
                BP->>BP: Clear Batch
                BP->>SC: Successful events
                SC->>SQS: Delete Processed Messages
            end
        else API Unhealthy (Circuit Open)
            SC->>SC: Pause Processing
            SC->>SC: Wait for Health Recovery
        end
    end
```

## Documentation

- **[SQSLibrary](SQSLibrary/README.md)**: Core library documentation

## Testing Strategy

The solution implements a testing strategy with **105 working tests** across unit and integration testing, including automatic configuration refresh functionality.

### Unit Tests (`SQSLibrary.Tests`) - 56 Tests ✅ **ALL PASSING**
**Core Functionality Tests:**
- **LogEvent Parsing** (8 tests): XML parsing, validation, error handling, timezone handling, multiple event types
- **S3EventParser** (17 tests): S3 event notification parsing, SNS unwrapping, URL decoding, edge cases
- **BatchProcessor** (13 tests): Ordering logic, batch triggers, null handling, complex sorting scenarios
- **CollectorHealthChecker** (15 tests): Health monitoring, circuit breaker, concurrent calls, recovery, timeout handling

### Queue Orchestrator Tests (`SQSConsumerService.Tests`) - 29 Tests ✅ **ALL PASSING**
**Queue Orchestrator Pattern Tests:**
- **Initialization** (2 tests): Configuration loading, queue validation, service provider integration
- **Lifecycle Management** (2 tests): Startup, shutdown, cleanup operations
- **Health Status** (3 tests): Overall status reporting, health details, status levels
- **Error Handling** (2 tests): Invalid operations, exception scenarios
- **Configuration Change Subscription** (2 tests): Automatic subscription/unsubscription to configuration changes

**Automatic Configuration Refresh Tests:**
- **ConfigurationChangeNotificationService** (8 tests): Pub-sub event system, subscriber management, error handling
- **ConfigurationChangeMonitor** (7 tests): Configuration monitoring, change detection, debouncing logic
- **Service Registration** (3 tests): Dependency injection, hosted service registration, settings configuration

**Testing Principles:**
- **Complete Isolation**: All external dependencies mocked (HTTP clients, AWS services)
- **Deterministic**: No real network calls or timing dependencies
- **Fast Execution**: All tests complete in under 5 seconds
- **Edge Case Coverage**: Malformed data, network errors, timeout scenarios

### Integration Tests (`SQSConsumerService.IntegrationTests`) - 20 Tests ✅ **ALL PASSING**
**End-to-End Functionality Tests:**
- **LocalStack Connectivity Tests** (2 tests): SQS connection and health endpoint verification
- **Component Integration Tests** (3 tests): SQSManager initialization, batch processing flow, S3 event parsing
- **Performance and Concurrency Tests** (2 tests): Parallel downloads and error handling
- **Queue Orchestrator Integration Tests** (4 tests): Full lifecycle testing, configuration validation, error handling
- **Automatic Configuration Refresh Integration** (6 tests): Service registration, event publishing, configuration monitoring
- **Configuration Change Detection** (3 tests): Queue configuration comparison and change detection logic

**⚠️ Integration Test Requirements:**
- **LocalStack REQUIRED**: Tests require LocalStack to be running on localhost:4566
- **Required Services**: S3, SQS, SNS, IAM services must be available in LocalStack
- **Setup Command**: Run `start-localstack.bat` or `docker-compose -f docker-compose.local.yml up localstack -d` before testing
- **Real Service Interaction**: Tests actual AWS service connectivity (via LocalStack)
- **Configuration Validation**: Ensures all configuration sections work together
- **Error Scenarios**: Tests failure handling with non-existent resources

**Running Tests:**
```bash
# Run unit tests (fast, isolated, no dependencies) - 56 tests
dotnet test SQSLibrary.Tests --verbosity normal

# Run service tests including configuration refresh (no dependencies) - 29 tests
dotnet test SQSConsumerService.Tests --verbosity normal

# Run configuration refresh tests only
dotnet test --filter "FullyQualifiedName~ConfigurationChange" --verbosity normal

# IMPORTANT: Start LocalStack before running integration tests
start-localstack.bat

# Run integration tests (REQUIRES LocalStack running) - 20 tests
dotnet test SQSConsumerService.IntegrationTests --verbosity normal

# Run all tests (REQUIRES LocalStack running) - 105 tests total
dotnet test SQSConsumerService.sln --verbosity normal

# Alternative: Run only unit tests (no LocalStack required)
dotnet test SQSLibrary.Tests
dotnet test SQSConsumerService.Tests

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

## Performance Characteristics

### Throughput Metrics
- **Parallel S3 Downloads**: 10x improvement with 10 concurrent downloads
- **Memory Usage**: ~100-200MB baseline, scales with batch size
- **CPU Utilization**: ~10-15% baseline, scales with message volume

### Scalability Considerations
- **Horizontal Scaling**: Multiple consumer instances can process different queues
- **Vertical Scaling**: Increase batch size and concurrent downloads for higher throughput
- **S3 Performance**: Supports 3,500 PUT/COPY/POST/DELETE and 5,500 GET/HEAD requests per second per prefix
- **SQS Limits**: 300 transactions per second (TPS) for standard queues

### Monitoring & Observability

#### Key Metrics to Monitor
- **Message Processing Rate**: Events processed per minute
- **Batch Processing Latency**: Time from SQS receive to collector POST
- **S3 Download Success Rate**: Percentage of successful downloads
- **Collector API Health**: Response time and error rate
- **Queue Depth**: Number of messages waiting in SQS

#### Health Endpoints
The service exposes health monitoring endpoints:
- **`GET /api/health`**: Comprehensive health check with service status, uptime, version, and consumer details
- **`GET /api/health/ping`**: Simple health check for load balancers (always returns 200 OK)
- **`GET /api/health/status`**: Detailed service status including active consumers and configuration
- **`GET /api/health/info`**: Detailed system information including runtime, process, and configuration details
- **`GET /api/health/version`**: Service version and build information


## API Endpoints

The service exposes several endpoints for monitoring and management:

### Health & Monitoring
- **GET /api/health** - Comprehensive health check with service status, uptime, and version
- **GET /api/health/ping** - Simple health check for load balancers
- **GET /api/health/status** - Detailed service status including active consumers and configuration
- **GET /api/health/info** - Detailed system information including runtime, process, and configuration details

### Version & About
- **GET /api/health/version** - Service version and build information

### Queue Management (Private Endpoints)
- **POST /api/queuemanagement/refresh** - Manual configuration refresh (automatic refresh also enabled)
- **GET /api/queuemanagement/summary** - Get current queue configuration summary (non-sensitive data)
- **POST /api/queuemanagement/restart** - Restart all consumers without checking for configuration changes
- **GET /api/queuemanagement/health/detailed** - Detailed health with queue names, client codes, and URLs

### Automatic Configuration Refresh
**Automatic Configuration Management**: The service uses AWS Secrets Manager's built-in auto-polling feature:
- **Polling Interval**: Configurable (default: 4 hours)
- **Change Detection**: Automatic detection of queue configuration changes
- **Pub-Sub Events**: Internal event system for notifying components of changes
- **Zero-Downtime Updates**: Consumers are automatically restarted when changes are detected

**Response Status Codes:**
- **200 OK**: Service is healthy and all consumers are operational
- **200 OK (Degraded)**: Service is operational but some consumers may be unhealthy
- **503 Service Unavailable**: Service is unhealthy (no active consumers or critical failures)
- **500 Internal Server Error**: Unexpected error occurred during health check

## Logging Levels by Environment

### Local Development
- **Default**: Debug
- **SQSLibrary**: Debug
- **SQSConsumerService**: Debug
- **Microsoft/System**: Warning

### Development
- **Default**: Debug
- **SQSLibrary**: Debug
- **SQSConsumerService**: Debug
- **Microsoft/System**: Warning

### Staging
- **Default**: Information
- **SQSLibrary**: Information
- **SQSConsumerService**: Information
- **Microsoft/System**: Warning

### Production
- **Default**: Warning
- **SQSLibrary**: Information
- **SQSConsumerService**: Information
- **Microsoft**: Warning
- **System**: Error

## Configuration Structure

The application now uses a modular configuration approach with separate settings for different concerns:

- **awsSettings** - AWS-specific configuration (region, LocalStack, credentials)
- **awsSecretsManager** - AWS Secrets Manager configuration (auto-polling, intervals)
- **configurationChangeMonitor** - Configuration change monitoring settings (debouncing, enabled)
- **sqsSettings** - SQS-specific settings (message limits, timeouts)
- **s3Settings** - S3-specific settings (download timeouts, retries)
- **batchProcessingSettings** - Batch processing configuration (sizes, concurrency)
- **collectorAPISettings** - Collector API configuration (URLs, endpoints, authentication)
- **healthCheckSettings** - Health check configuration (intervals, timeouts, failure thresholds)