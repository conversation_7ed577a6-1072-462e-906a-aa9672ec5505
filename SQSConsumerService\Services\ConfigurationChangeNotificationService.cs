using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Event arguments for configuration change notifications
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Timestamp when the configuration change was detected
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Type of configuration that changed
        /// </summary>
        public string ConfigurationType { get; set; } = "QueueConfiguration";

        /// <summary>
        /// Additional details about the change
        /// </summary>
        public string Details { get; set; }
    }

    /// <summary>
    /// Interface for configuration change notification service
    /// </summary>
    public interface IConfigurationChangeNotificationService
    {
        /// <summary>
        /// Event raised when configuration changes are detected
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// Publishes a configuration changed notification
        /// </summary>
        /// <param name="details">Optional details about the change</param>
        /// <returns>Task representing the publish operation</returns>
        Task PublishConfigurationChangedAsync(string details = null);

        /// <summary>
        /// Subscribes to configuration change notifications
        /// </summary>
        /// <param name="handler">Event handler to subscribe</param>
        void Subscribe(EventHandler<ConfigurationChangedEventArgs> handler);

        /// <summary>
        /// Unsubscribes from configuration change notifications
        /// </summary>
        /// <param name="handler">Event handler to unsubscribe</param>
        void Unsubscribe(EventHandler<ConfigurationChangedEventArgs> handler);
    }

    /// <summary>
    /// Service for managing configuration change notifications using pub-sub pattern
    /// </summary>
    public class ConfigurationChangeNotificationService : IConfigurationChangeNotificationService
    {
        private readonly ILogger<ConfigurationChangeNotificationService> _logger;
        private readonly object _lock = new object();

        /// <summary>
        /// Event raised when configuration changes are detected
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// Initializes a new instance of the ConfigurationChangeNotificationService
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <exception cref="ArgumentNullException">Thrown when logger is null</exception>
        public ConfigurationChangeNotificationService(ILogger<ConfigurationChangeNotificationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Publishes a configuration changed notification to all subscribers
        /// </summary>
        /// <param name="details">Optional details about the change</param>
        /// <returns>Task representing the publish operation</returns>
        public async Task PublishConfigurationChangedAsync(string details = null)
        {
            var eventArgs = new ConfigurationChangedEventArgs
            {
                Details = string.IsNullOrEmpty(details) ? "Queue configuration changed" : details
            };

            _logger.LogInformation("Publishing configuration change notification: {Details}", eventArgs.Details);

            // Get a copy of the event handlers to avoid issues with concurrent modifications
            EventHandler<ConfigurationChangedEventArgs> handlers;
            lock (_lock)
            {
                handlers = ConfigurationChanged;
            }

            if (handlers != null)
            {
                var invocationList = handlers.GetInvocationList();
                var tasks = new List<Task>();

                foreach (EventHandler<ConfigurationChangedEventArgs> handler in invocationList)
                {
                    tasks.Add(Task.Run(() =>
                    {
                        try
                        {
                            handler(this, eventArgs);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error occurred while notifying configuration change subscriber");
                        }
                    }));
                }

                await Task.WhenAll(tasks);
                _logger.LogDebug("Configuration change notification sent to {SubscriberCount} subscribers", invocationList.Length);
            }
            else
            {
                _logger.LogDebug("No subscribers for configuration change notifications");
            }
        }

        /// <summary>
        /// Subscribes to configuration change notifications
        /// </summary>
        /// <param name="handler">Event handler to subscribe</param>
        /// <exception cref="ArgumentNullException">Thrown when handler is null</exception>
        public void Subscribe(EventHandler<ConfigurationChangedEventArgs> handler)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            lock (_lock)
            {
                ConfigurationChanged += handler;
            }

            _logger.LogDebug("New subscriber added for configuration change notifications");
        }

        /// <summary>
        /// Unsubscribes from configuration change notifications
        /// </summary>
        /// <param name="handler">Event handler to unsubscribe</param>
        /// <exception cref="ArgumentNullException">Thrown when handler is null</exception>
        public void Unsubscribe(EventHandler<ConfigurationChangedEventArgs> handler)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            lock (_lock)
            {
                ConfigurationChanged -= handler;
            }

            _logger.LogDebug("Subscriber removed from configuration change notifications");
        }
    }
}
