using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SQSLibrary;
using SQSLibrary.Configuration;
using SQSConsumerService.Code;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Queue orchestrator that centralizes SQS consumer management, health checking, and lifecycle operations
    /// </summary>
    public partial class QueueOrchestrator : IQueueOrchestrator
    {
        private readonly ILogger<QueueOrchestrator> _logger;
        private readonly List<SQSManager> _activeConsumers;
        private readonly object _lock = new object();

        private ISharedHealthCheckerService _sharedHealthChecker;
        private QueueDefinition[] _configuredQueues;
        private bool _disposed = false;
        private bool _initialized = false;

        // Configuration objects (injected)
        private readonly AwsSettings _awsSettings;
        private readonly SQSSettings _sqsSettings;
        private readonly S3Settings _s3Settings;
        private readonly BatchProcessingSettings _batchSettings;
        private readonly HealthCheckSettings _healthSettings;
        private readonly CollectorAPISettings _collectorAPISettings;
        private readonly IConfigurationChangeNotificationService _configurationChangeNotificationService;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Initializes a new instance of the QueueOrchestrator class
        /// </summary>
        /// <param name="logger">Logger instance for logging orchestrator operations</param>
        /// <param name="awsSettings">AWS configuration settings including region and LocalStack options</param>
        /// <param name="sqsSettings">SQS-specific settings such as message limits and timeouts</param>
        /// <param name="s3Settings">S3 configuration for file downloads and retry policies</param>
        /// <param name="batchSettings">Batch processing configuration including size and timeout settings</param>
        /// <param name="healthSettings">Health check configuration for monitoring collector API availability</param>
        /// <param name="collectorAPISettings">Collector API configuration including endpoints and authentication</param>
        /// <param name="configurationChangeNotificationService">Service for receiving configuration change notifications</param>
        /// <param name="configuration">Configuration instance updated by Kralizek polling</param>
        /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
        public QueueOrchestrator(
            ILogger<QueueOrchestrator> logger,
            AwsSettings awsSettings,
            SQSSettings sqsSettings,
            S3Settings s3Settings,
            BatchProcessingSettings batchSettings,
            HealthCheckSettings healthSettings,
            CollectorAPISettings collectorAPISettings,
            IConfigurationChangeNotificationService configurationChangeNotificationService,
            IConfiguration configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _awsSettings = awsSettings ?? throw new ArgumentNullException(nameof(awsSettings));
            _sqsSettings = sqsSettings ?? throw new ArgumentNullException(nameof(sqsSettings));
            _s3Settings = s3Settings ?? throw new ArgumentNullException(nameof(s3Settings));
            _batchSettings = batchSettings ?? throw new ArgumentNullException(nameof(batchSettings));
            _healthSettings = healthSettings ?? throw new ArgumentNullException(nameof(healthSettings));
            _collectorAPISettings = collectorAPISettings ?? throw new ArgumentNullException(nameof(collectorAPISettings));
            _configurationChangeNotificationService = configurationChangeNotificationService ?? throw new ArgumentNullException(nameof(configurationChangeNotificationService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _activeConsumers = new List<SQSManager>();

            // Subscribe to configuration change notifications
            _configurationChangeNotificationService.Subscribe(OnConfigurationChanged);
        }

        /// <summary>
        /// Gets the collection of active SQS consumers
        /// </summary>
        public IReadOnlyList<SQSManager> ActiveConsumers
        {
            get
            {
                lock (_lock)
                {
                    return _activeConsumers.AsReadOnly();
                }
            }
        }

        /// <summary>
        /// Gets the number of configured queues
        /// </summary>
        public int ConfiguredQueueCount => _configuredQueues?.Length ?? 0;

        /// <summary>
        /// Gets the number of healthy consumers
        /// </summary>
        public int HealthyConsumerCount
        {
            get
            {
                lock (_lock)
                {
                    return _activeConsumers.Count(c => c.IsHealthy);
                }
            }
        }

        /// <summary>
        /// Gets the number of failed consumers
        /// </summary>
        public int FailedConsumerCount => ConfiguredQueueCount - _activeConsumers.Count;

        /// <summary>
        /// Gets the overall health status of all consumers
        /// </summary>
        public bool IsHealthy
        {
            get
            {
                lock (_lock)
                {
                    return ConfiguredQueueCount > 0 &&
                           _activeConsumers.Count == ConfiguredQueueCount &&
                           _activeConsumers.All(c => c.IsHealthy);
                }
            }
        }

        /// <summary>
        /// Gets the overall status (Healthy, Degraded, Unhealthy)
        /// </summary>
        public HealthStatus OverallStatus
        {
            get
            {
                lock (_lock)
                {
                    var activeCount = _activeConsumers.Count;
                    var configuredCount = ConfiguredQueueCount;
                    var healthyCount = HealthyConsumerCount;

                    if (activeCount == 0)
                        return HealthStatus.Unhealthy;

                    if (activeCount == configuredCount && healthyCount == activeCount)
                        return HealthStatus.Healthy;

                    return HealthStatus.Degraded;
                }
            }
        }

        /// <summary>
        /// Initialize the orchestrator with configuration and required services
        /// This method must be called before any other operations can be performed
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection to resolve required services</param>
        /// <returns>Task representing the asynchronous initialization operation</returns>
        /// <exception cref="InvalidOperationException">Thrown when orchestrator is already initialized</exception>
        /// <exception cref="ArgumentNullException">Thrown when required services are not registered in DI container</exception>
        /// <exception cref="Exception">Thrown when configuration loading or service initialization fails</exception>
        public async Task InitializeAsync(IServiceProvider serviceProvider)
        {
            if (_initialized)
            {
                _logger.LogWarning("Queue orchestrator already initialized");
                return;
            }

            try
            {
                // Get required services from DI
                _sharedHealthChecker = (ISharedHealthCheckerService)serviceProvider.GetService(typeof(ISharedHealthCheckerService))
                    ?? throw new InvalidOperationException("ISharedHealthCheckerService not registered");

                // Log configuration information (configuration objects are already injected)
                _logger.LogInformation("Configuration loaded - AWS Region: {Region}, LocalStack: {UseLocalStack}",
                    _awsSettings.Region, _awsSettings.UseLocalStack);

                // Initialize shared health checker
                InitializeHealthChecker();

                // Load queue configurations
                await LoadQueueConfigurationsAsync();

                _initialized = true;
                _logger.LogInformation("Queue orchestrator initialized successfully with {QueueCount} queues", ConfiguredQueueCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize queue orchestrator");
                throw;
            }
        }

        /// <summary>
        /// Start all SQS consumers for the configured queues
        /// Creates and starts an SQSManager instance for each configured queue
        /// Failed consumer startups are logged but do not prevent other consumers from starting
        /// </summary>
        /// <returns>Task representing the asynchronous startup operation</returns>
        /// <exception cref="InvalidOperationException">Thrown when orchestrator has not been initialized</exception>
        public async Task StartConsumersAsync()
        {
            if (!_initialized)
                throw new InvalidOperationException("Queue orchestrator must be initialized before starting consumers");

            _logger.LogInformation("Starting SQS consumers for {QueueCount} queues...", ConfiguredQueueCount);

            var startedCount = 0;
            var failedCount = 0;

            foreach (var queue in _configuredQueues)
            {
                try
                {
                    var sqsManager = CreateSQSManager(queue);
                    var clientEventsUrl = BuildClientEventsUrl(queue.ClientCode);
                    
                    await sqsManager.StartConsumingAsync(clientEventsUrl);

                    lock (_lock)
                    {
                        _activeConsumers.Add(sqsManager);
                    }

                    startedCount++;
                    _logger.LogInformation("Successfully started SQS consumer for queue: {QueueName}", queue.QueueName);
                }
                catch (Exception ex)
                {
                    failedCount++;
                    _logger.LogError(ex, "Failed to start SQS consumer for queue: {QueueName}", queue.QueueName);
                }
            }

            _logger.LogInformation("SQS consumer startup completed. Started: {StartedCount}, Failed: {FailedCount}", 
                startedCount, failedCount);
        }

        /// <summary>
        /// Stop all active SQS consumers gracefully
        /// Disposes all SQSManager instances and clears the active consumers list
        /// Operations are performed in parallel for faster shutdown
        /// </summary>
        /// <returns>Task representing the asynchronous shutdown operation</returns>
        public async Task StopConsumersAsync()
        {
            _logger.LogInformation("Stopping all SQS consumers...");

            List<SQSManager> consumersToStop;
            lock (_lock)
            {
                consumersToStop = new List<SQSManager>(_activeConsumers);
                _activeConsumers.Clear();
            }

            var tasks = consumersToStop.Select(consumer => Task.Run(() =>
            {
                try
                {
                    consumer.Dispose();
                    _logger.LogDebug("Stopped SQS consumer for queue: {QueueName}", consumer.QueueDefinition.QueueName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error stopping SQS consumer for queue: {QueueName}", consumer.QueueDefinition.QueueName);
                }
            }));

            await Task.WhenAll(tasks);
            _logger.LogInformation("All SQS consumers stopped");
        }



        /// <summary>
        /// Refresh queue configurations and restart consumers if changes are detected
        /// Compares current configuration with new configuration and updates consumers accordingly
        /// </summary>
        /// <returns>Task representing the asynchronous refresh operation</returns>
        /// <exception cref="InvalidOperationException">Thrown when orchestrator is not initialized</exception>
        public async Task<bool> RefreshQueueConfigurationsAsync()
        {
            if (!_initialized)
                throw new InvalidOperationException("Queue orchestrator must be initialized before refreshing configurations");

            _logger.LogInformation("Refreshing queue configurations...");

            try
            {
                // Load new configurations from IConfiguration (updated by Kralizek)
                var newQueueCollection = LoadQueueDefinitionsFromConfiguration();
                var newQueues = newQueueCollection.ToArray();

                // Compare with current configurations
                var hasChanges = DetectConfigurationChanges(_configuredQueues, newQueues);

                if (hasChanges)
                {
                    _logger.LogInformation("Configuration changes detected. Restarting consumers...");

                    // Stop current consumers
                    await StopConsumersAsync();

                    // Update configuration
                    _configuredQueues = newQueues;

                    // Start consumers with new configuration
                    await StartConsumersAsync();

                    _logger.LogInformation("Queue configuration refresh completed successfully");
                    return true;
                }
                else
                {
                    _logger.LogInformation("No configuration changes detected");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh queue configurations");
                throw;
            }
        }

        /// <summary>
        /// Handles configuration change notifications and automatically restarts consumers if needed
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Configuration change event arguments</param>
        private async void OnConfigurationChanged(object sender, ConfigurationChangedEventArgs e)
        {
            try
            {
                _logger.LogInformation("Configuration change notification received: {Details}", e.Details);

                if (_initialized)
                {
                    // Automatically refresh queue configurations
                    var hasChanges = await RefreshQueueConfigurationsAsync();
                    _logger.LogInformation("Automatic configuration refresh completed. Changes applied: {HasChanges}", hasChanges);
                }
                else
                {
                    _logger.LogDebug("Skipping configuration refresh - orchestrator not yet initialized");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling configuration change notification");
            }
        }

        /// <summary>
        /// Perform comprehensive cleanup operations
        /// Stops all consumers and shared health checker service
        /// Should be called during application shutdown or service disposal
        /// </summary>
        /// <returns>Task representing the asynchronous cleanup operation</returns>
        public async Task CleanupAsync()
        {
            _logger.LogInformation("Performing queue orchestrator cleanup...");

            // Unsubscribe from configuration change notifications
            _configurationChangeNotificationService?.Unsubscribe(OnConfigurationChanged);

            await StopConsumersAsync();

            _sharedHealthChecker?.Stop();

            _logger.LogInformation("Queue orchestrator cleanup completed");
        }

        /// <summary>
        /// Get detailed health information for all consumers and the overall service status
        /// Returns comprehensive health data including individual consumer status and collector health
        /// Thread-safe operation that provides a snapshot of current health state
        /// </summary>
        /// <returns>Anonymous object containing detailed health information including consumer details and overall status</returns>
        public object GetHealthDetails()
        {
            lock (_lock)
            {
                var consumers = _activeConsumers.Select(c => new
                {
                    QueueName = c.QueueDefinition.QueueName,
                    ClientCode = c.QueueDefinition.ClientCode,
                    IsHealthy = c.IsHealthy,
                    QueueUrl = c.QueueDefinition.QueueUrl
                }).ToArray();

                return new
                {
                    OverallStatus = OverallStatus.ToLowerString(),
                    IsHealthy = IsHealthy,
                    ConfiguredQueues = ConfiguredQueueCount,
                    ActiveConsumers = _activeConsumers.Count,
                    HealthyConsumers = HealthyConsumerCount,
                    FailedConsumers = FailedConsumerCount,
                    CollectorHealthy = _sharedHealthChecker?.IsHealthy ?? false,
                    Consumers = consumers
                };
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Detect if there are changes between current and new queue configurations
        /// </summary>
        /// <param name="currentQueues">Current queue configurations</param>
        /// <param name="newQueues">New queue configurations</param>
        /// <returns>True if changes are detected, false otherwise</returns>
        private static bool DetectConfigurationChanges(QueueDefinition[] currentQueues, QueueDefinition[] newQueues)
        {
            if (currentQueues == null || newQueues == null)
                return true;

            if (currentQueues.Length != newQueues.Length)
                return true;

            // Compare each queue configuration
            foreach (var current in currentQueues)
            {
                var newQueue = newQueues.FirstOrDefault(q => q.ClientCode == current.ClientCode);

                if (newQueue == null)
                    return true; // Queue removed

                if (current.QueueName != newQueue.QueueName ||
                    current.QueueUrl != newQueue.QueueUrl ||
                    current.Region != newQueue.Region)
                    return true; // Queue configuration changed
            }

            // Check for new queues
            return newQueues.Any(newQueue => !currentQueues.Any(q => q.ClientCode == newQueue.ClientCode));
        }

        /// <summary>
        /// Initialize the shared health checker
        /// </summary>
        private void InitializeHealthChecker()
        {
            if (_healthSettings.Enabled)
            {
                var healthUrl = _collectorAPISettings.GetHealthUrl();
                _logger.LogInformation("Initializing shared health checker for URL: {HealthUrl}", healthUrl);

                _sharedHealthChecker.Initialize(
                    healthUrl,
                    TimeSpan.FromSeconds(_healthSettings.IntervalSeconds),
                    TimeSpan.FromSeconds(_healthSettings.TimeoutSeconds),
                    _healthSettings.MaxConsecutiveFailures);
            }
            else
            {
                _logger.LogInformation("Health checking is disabled");
            }
        }

        /// <summary>
        /// Load queue configurations directly from IConfiguration (updated by Kralizek or local config)
        /// </summary>
        private Task LoadQueueConfigurationsAsync()
        {
            _logger.LogDebug("Loading queue configurations...");

            var queueCollection = LoadQueueDefinitionsFromConfiguration();
            _configuredQueues = queueCollection.ToArray();

            _logger.LogInformation("Loaded {QueueCount} queue configurations", _configuredQueues.Length);
            return Task.CompletedTask;
        }

        /// <summary>
        /// Loads queue definitions directly from IConfiguration (updated by Kralizek polling).
        /// This method reads from the "ClientQueues" configuration section and validates each queue definition.
        /// </summary>
        /// <returns>List of validated queue definitions or empty list if none found</returns>
        /// <exception cref="Exception">Logs warnings for invalid queue configurations but does not throw</exception>
        private List<QueueDefinition> LoadQueueDefinitionsFromConfiguration()
        {
            try
            {
                var queues = new List<QueueDefinition>();
                var clientQueuesSection = _configuration.GetSection("ClientQueues");

                if (clientQueuesSection.Value != null || clientQueuesSection.GetChildren().Any())
                {
                    foreach (var queueSection in clientQueuesSection.GetChildren())
                    {
                        var queue = new QueueDefinition
                        {
                            ClientCode = queueSection["ClientCode"],
                            QueueName = queueSection["QueueName"],
                            QueueUrl = queueSection["QueueUrl"],
                            Region = queueSection["Region"]
                        };

                        // Validate required fields
                        if (!string.IsNullOrEmpty(queue.ClientCode) &&
                            !string.IsNullOrEmpty(queue.QueueName) &&
                            !string.IsNullOrEmpty(queue.QueueUrl))
                        {
                            queues.Add(queue);
                        }
                        else
                        {
                            _logger.LogWarning("Skipping invalid queue configuration: ClientCode={ClientCode}, QueueName={QueueName}, QueueUrl={QueueUrl}",
                                queue.ClientCode, queue.QueueName, queue.QueueUrl);
                        }
                    }
                }

                _logger.LogDebug("Loaded {QueueCount} queue definitions from IConfiguration", queues.Count);
                return queues;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load queue definitions from IConfiguration");
                return new List<QueueDefinition>();
            }
        }

        /// <summary>
        /// Create an SQSManager instance for a queue
        /// </summary>
        private SQSManager CreateSQSManager(QueueDefinition queue)
        {
            return new SQSManager(
                _awsSettings,
                _sqsSettings,
                _s3Settings,
                _batchSettings,
                _healthSettings,
                _collectorAPISettings,
                queue,
                _sharedHealthChecker);
        }

        /// <summary>
        /// Build the collector events URL for a specific client
        /// </summary>
        private string BuildClientEventsUrl(string clientCode)
        {
            var clientEventsUrl = _collectorAPISettings.GetClientEventsUrl(clientCode);
            var decryptedUserId = _collectorAPISettings.GetDecryptedUserId();
            var decryptedApiKey = _collectorAPISettings.GetDecryptedApiKey();

            if (!string.IsNullOrEmpty(decryptedUserId) && !string.IsNullOrEmpty(decryptedApiKey))
            {
                clientEventsUrl += $"?userid={decryptedUserId}&rawApiKey={decryptedApiKey}";

                _logger.LogDebug("Built collector URL with separate userId and API key for client {ClientCode}", clientCode);
            }
            else
            {
                _logger.LogWarning("Either userId or API key is not configured for client {ClientCode}. " +
                    "Events will not be sent to the collector for this client.", clientCode);
            }
            return clientEventsUrl;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing && !_disposed)
            {
                try
                {
                    CleanupAsync().GetAwaiter().GetResult();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during queue orchestrator disposal");
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
