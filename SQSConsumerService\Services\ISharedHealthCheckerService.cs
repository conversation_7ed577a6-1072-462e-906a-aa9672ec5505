using System;
using SQSLibrary.Services;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Interface for shared health checker service that manages a single health checker instance
    /// for all SQS consumers to avoid duplicate health checks
    /// </summary>
    public interface ISharedHealthCheckerService : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    {
        /// <summary>
        /// Initialize the health checker with the given settings
        /// </summary>
        /// <param name="healthCheckUrl">URL to check for health</param>
        /// <param name="healthCheckInterval">Interval between health checks</param>
        /// <param name="healthCheckTimeout">Timeout for health check requests</param>
        /// <param name="maxConsecutiveFailures">Maximum consecutive failures before marking as unhealthy</param>
        void Initialize(string healthCheckUrl, TimeSpan healthCheckInterval, TimeSpan healthCheckTimeout, int maxConsecutiveFailures);

        /// <summary>
        /// Stop the health checker
        /// </summary>
        void Stop();
    }
}
