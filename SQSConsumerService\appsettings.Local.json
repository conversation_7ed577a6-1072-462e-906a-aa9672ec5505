{"Logging": {"LogLevel": {"Default": "Debug", "System": "Warning", "Microsoft": "Warning", "SQSLibrary": "Debug", "SQSConsumerService": "Debug"}, "elasticsearchSettings": {"url": "http://localhost:9200", "userName": "", "password": ""}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "SQSLibrary": "Debug", "SQSConsumerService": "Debug"}}, "Properties": {"ApplicationName": "SQSConsumerService", "Instance": "Local", "Environment": "Local"}, "WriteTo": [{"Name": "File", "Args": {"restrictedToMinimumLevel": "Debug", "path": "Logs/logs_.log", "rollingInterval": "Day", "retainedFileCountLimit": 31}}]}, "awsSecretsManager": {"secretName": "local/us/csa-sqs-consumer-service/configuration", "fallbackToLocal": true, "pollingIntervalMinutes": 1}, "ClientQueues": [{"clientCode": "brmb", "queueName": "local-washingtoncounty-logevents-queue", "queueUrl": "http://localhost:4566/000000000000/local-washingtoncounty-logevents-queue", "region": "us-east-1"}, {"clientCode": "flfl", "queueName": "local-flaglercounty-logevents-queue", "queueUrl": "http://localhost:4566/000000000000/local-flaglercounty-logevents-queue", "region": "us-east-1"}], "awsSettings": {"accessKey": "test", "secretKey": "test", "region": "us-east-1", "useLocalStack": true, "serviceUrl": "http://localhost:4566"}, "sqsSettings": {"maxMessages": 1, "waitTimeSeconds": 20, "visibilityTimeoutSeconds": 30}, "s3Settings": {"downloadTimeoutSeconds": 30, "downloadRetryAttempts": 3}, "batchProcessingSettings": {"enableBatchProcessing": true, "batchSize": 5, "batchTimeoutMs": 5000, "enableParallelS3Downloads": true, "maxConcurrentS3Downloads": 5}, "collectorAPISettings": {"baseUrl": "http://localhost:62080", "eventsEndpoint": "/api/events/{clientCode}", "healthEndpoint": "/api/health/ping", "userId": "14c/bpnwiR8rgjvUyzc6QHcnSztStAhgtUMBu0YxulgXqsi+mNgMB/fxPJy0U5wrXri7MBz40CqvJyvBX253/g==", "apiKey": "hJhLdy3acCKTavP325kvbTofFyIXKa7X4OO8cUThfqbyHr+wIaiIW9XPJS3eBIVGQYp/ZuDgPTuN3IFbOgclNA==", "timeoutSeconds": 30}, "healthCheckSettings": {"enabled": true, "intervalSeconds": 30, "timeoutSeconds": 10, "maxConsecutiveFailures": 3}}