using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Serilog;
using SQSLibrary.Logging;
using SQSLibrary.Services;

namespace SQSLibrary
{
    /// <summary>
    /// Handles batch processing and ordering of LogEvents
    /// </summary>
    public class BatchProcessor
    {
        private static readonly ILogger _logger = LoggerFactory.ForContext<BatchProcessor>();
        private readonly List<LogEvent> _batch;
        private readonly int _maxBatchSize;
        private readonly int _batchTimeoutMs;
        private DateTime _batchStartTime;

        /// <summary>
        /// Initializes a new instance of the BatchProcessor class.
        /// </summary>
        /// <param name="maxBatchSize">Maximum number of events in a batch before triggering processing</param>
        /// <param name="batchTimeoutMs">Timeout in milliseconds before triggering batch processing</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when maxBatchSize is less than 1 or batchTimeoutMs is less than 0</exception>
        public BatchProcessor(int maxBatchSize, int batchTimeoutMs)
        {
            _batch = new List<LogEvent>();
            _maxBatchSize = maxBatchSize;
            _batchTimeoutMs = batchTimeoutMs;
            _batchStartTime = DateTime.UtcNow;
        }

        /// <summary>
        /// Add S3 download results to the batch for processing.
        /// Parses XML content from successful downloads and adds valid LogEvents to the batch.
        /// </summary>
        /// <param name="downloadResults">S3 download results containing LogEvent XML content</param>
        /// <returns>List of LogEvents that were successfully parsed and added to the batch</returns>
        /// <exception cref="ArgumentNullException">downloadResults can be null (returns empty list)</exception>
        public List<LogEvent> AddS3DownloadResults(IEnumerable<S3DownloadResult> downloadResults)
        {
            var parsedEvents = new List<LogEvent>();

            if (downloadResults == null)
            {
                return parsedEvents;
            }

            foreach (var result in downloadResults)
            {
                if (!result.IsSuccess)
                {
                    _logger.Warning("Skipping failed S3 download: {S3Object} - {ErrorMessage}",
                        result.S3Object, result.ErrorMessage);
                    continue;
                }

                // Parse LogEvent from S3 XML content
                _logger.Debug($"Creating LogEvent with SqsReceiptHandle: {{ReceiptHandle}}, SqsMessageId: {{MessageId}}, ObjectKey: {{ObjectKey}}",
                    result.S3Object.SqsReceiptHandle, result.S3Object.SqsMessageId, result.S3Object.ObjectKey);
                var logEvent = LogEvent.ParseFromS3Content(result.XmlContent, result.S3Object.SqsMessageId, result.S3Object.SqsReceiptHandle);

                if (logEvent != null)
                {
                    _batch.Add(logEvent);
                    parsedEvents.Add(logEvent);

                    if (_batch.Count == 1) // First event in batch
                    {
                        _batchStartTime = DateTime.UtcNow;
                    }

                    _logger.Debug("Parsed LogEvent from S3 object {S3Object}: {EventType} at {Timestamp}",
                        result.S3Object, logEvent.EventType, logEvent.Timestamp);
                }
                else
                {
                    _logger.Warning("Failed to parse LogEvent from S3 object {S3Object}", result.S3Object);
                }
            }

            _logger.Debug("Added {ParsedCount} events to batch from {ResultCount} S3 downloads. Current batch size: {BatchSize}",
                parsedEvents.Count, downloadResults.Count(), _batch.Count);
            return parsedEvents;
        }

        /// <summary>
        /// Check if batch is ready for processing
        /// </summary>
        /// <returns>True if batch should be processed</returns>
        public bool IsBatchReady()
        {
            if (_batch.Count == 0)
                return false;

            // Process if batch is full
            if (_batch.Count >= _maxBatchSize)
            {
                _logger.Debug("Batch ready: reached max size ({BatchCount}/{MaxBatchSize})", _batch.Count, _maxBatchSize);
                return true;
            }

            // Process if timeout reached
            var elapsed = DateTime.UtcNow - _batchStartTime;
            if (elapsed.TotalMilliseconds >= _batchTimeoutMs)
            {
                _logger.Debug("Batch ready: timeout reached ({ElapsedMs}ms >= {TimeoutMs}ms)",
                    elapsed.TotalMilliseconds, _batchTimeoutMs);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Get ordered batch of events and clear the batch
        /// </summary>
        /// <returns>Ordered list of LogEvents</returns>
        public List<LogEvent> GetOrderedBatch()
        {
            if (_batch.Count == 0)
                return new List<LogEvent>();

            // Sort by timestamp first, then by call identifier (ASC)
            var orderedEvents = _batch
                .OrderBy(e => e.Timestamp)
                .ThenBy(e => e.CallIdentifier, StringComparer.Ordinal)
                .ToList();

            _logger.Information("Processing ordered batch of {EventCount} events. Time range: {StartTime:HH:mm:ss.fff} - {EndTime:HH:mm:ss.fff}",
                orderedEvents.Count, orderedEvents[0].Timestamp, orderedEvents[^1].Timestamp);

            // Clear the batch
            _batch.Clear();
            _batchStartTime = DateTime.UtcNow;

            return orderedEvents;
        }

        /// <summary>
        /// Process the ordered batch by sending events to collector API
        /// </summary>
        /// <param name="orderedEvents">Ordered list of LogEvents</param>
        /// <param name="collectorURL">Collector API URL</param>
        /// <returns>List of successfully processed events</returns>
        public static async Task<List<LogEvent>> ProcessOrderedBatch(List<LogEvent> orderedEvents, string collectorURL)
        {
            var successfulEvents = new List<LogEvent>();

            foreach (var logEvent in orderedEvents)
            {
                try
                {
                    bool success = await PushEvent.ToCollectorAPI(collectorURL, logEvent.OriginalXml);
                    if (success)
                    {
                        successfulEvents.Add(logEvent);
                        _logger.Verbose("Successfully sent ordered event: {LogEvent}", logEvent);
                    }
                    else
                    {
                        _logger.Warning("Failed to send ordered event: {LogEvent}", logEvent);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Exception sending ordered event {LogEvent}", logEvent);
                }
            }

            _logger.Information("Batch processing complete: {SuccessCount}/{TotalCount} events sent successfully",
                successfulEvents.Count, orderedEvents.Count);
            return successfulEvents;
        }

        /// <summary>
        /// Get current batch size
        /// </summary>
        public int CurrentBatchSize => _batch.Count;

        /// <summary>
        /// Get time since batch started
        /// </summary>
        public TimeSpan BatchAge => DateTime.UtcNow - _batchStartTime;
    }
}
