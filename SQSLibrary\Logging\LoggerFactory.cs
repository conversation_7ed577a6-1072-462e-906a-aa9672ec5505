using Serilog;
using System;

namespace SQSLibrary.Logging
{
    /// <summary>
    /// Factory for creating loggers with proper source context
    /// </summary>
    public static class LoggerFactory
    {
        /// <summary>
        /// Creates a logger for the specified type with proper source context
        /// </summary>
        /// <typeparam name="T">The type to create a logger for</typeparam>
        /// <returns>Logger with source context set to the type name</returns>
        public static ILogger ForContext<T>()
        {
            return Log.ForContext<T>();
        }

        /// <summary>
        /// Creates a logger for the specified type with proper source context
        /// </summary>
        /// <param name="type">The type to create a logger for</param>
        /// <returns>Logger with source context set to the type name</returns>
        public static ILogger ForContext(Type type)
        {
            return Log.ForContext(type);
        }

        /// <summary>
        /// Creates a logger with the specified source context
        /// </summary>
        /// <param name="sourceContext">The source context to use</param>
        /// <returns>Logger with the specified source context</returns>
        public static ILogger ForContext(string sourceContext)
        {
            return Log.ForContext("SourceContext", sourceContext);
        }
    }
}
