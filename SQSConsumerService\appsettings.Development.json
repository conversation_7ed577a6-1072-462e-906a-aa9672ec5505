{"Logging": {"LogLevel": {"Default": "Debug", "System": "Warning", "Microsoft": "Warning", "SQSLibrary": "Debug", "SQSConsumerService": "Debug"}, "elasticsearchSettings": {"url": "zrbdQPOosF6YkSXVAI9nhiCmFhC9ozTB01tnh9mE/fxVvi3fOFgf8ckQmDDfEMnXUAdroMEhIvRzdQMVQzq5A/XGA88j2cGbqsFITfTRksiU6zmbgS8uB9yjLUhZrKW5", "userName": "Ny5ym1gEy7eE5ska83+O9pc5PDUzYw3TeRfP11LDJ8A=", "password": "veZV3sHVDrFH8BFWINgeL2VBuHdUyLPh0+JV+MnW6Hn1TV9/0UMA56blVYFKdQm8"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "SQSLibrary": "Debug", "SQSConsumerService": "Debug"}}, "Properties": {"ApplicationName": "SQSConsumerService", "Instance": "Development", "Environment": "Development"}}, "awsSecretsManager": {"secretName": "dev/us/csa-sqs-consumer-service/configuration"}}