﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using SQSConsumerService.Code;
using SQSConsumerService.Services;
using System;
using System.Reflection;
using System.Runtime.InteropServices;

namespace SQSConsumerService.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HealthController : ControllerBase
    {
        private const string ServiceName = "SQS Consumer Service";
        private const string UnknownValue = "unknown";

        private readonly IWebHostEnvironment _env;
        private readonly IConfiguration _configuration;
        private readonly IQueueOrchestrator _queueOrchestrator;

        public HealthController(IConfiguration configuration, IWebHostEnvironment env, IQueueOrchestrator queueOrchestrator)
        {
            _configuration = configuration;
            _env = env;
            _queueOrchestrator = queueOrchestrator;
        }

        [HttpGet("ping")]
        public IActionResult GetPing()
        {
            var healthyStatus = HealthStatus.Healthy.ToLowerString();
            return Ok(new { status = healthyStatus, message = $"{ServiceName}  is {healthyStatus}", timestamp = DateTime.UtcNow });
        }

        [HttpGet]
        public IActionResult GetHealth()
        {
            try
            {
                var overallStatus = _queueOrchestrator.OverallStatus;
                var statusString = overallStatus.ToLowerString();

                var healthData = new
                {
                    status = statusString,
                    service = ServiceName,
                    environment = _env.EnvironmentName,
                    timestamp = DateTime.UtcNow,
                    uptime = DateTime.UtcNow - System.Diagnostics.Process.GetCurrentProcess().StartTime.ToUniversalTime(),
                    version = _configuration.GetSection("version").Value ?? UnknownValue,
                    build = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? UnknownValue,
                    consumers = new
                    {
                        active = _queueOrchestrator.ActiveConsumers.Count,
                        configured = _queueOrchestrator.ConfiguredQueueCount,
                        healthy = _queueOrchestrator.HealthyConsumerCount,
                        failed = _queueOrchestrator.FailedConsumerCount
                    }
                };

                // Return appropriate HTTP status code
                return overallStatus switch
                {
                    HealthStatus.Unhealthy => StatusCode(503, healthData),
                    HealthStatus.Degraded => StatusCode(200, healthData), // OK but with warnings
                    HealthStatus.Healthy => Ok(healthData),
                    _ => Ok(healthData)
                };
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { status = HealthStatus.Unhealthy.ToLowerString(), error = ex.Message, timestamp = DateTime.UtcNow });
            }
        }

        [HttpGet("version")]
        public IActionResult GetVersion()
        {
            var data = new AboutApiData
            {
                Environment = _env.EnvironmentName,
                Name = ServiceName,
                Version = _configuration.GetSection("version").Value,
                Build = Assembly.GetExecutingAssembly().GetName().Version?.ToString()
            };

            return Ok(data);
        }

        [HttpGet("info")]
        public IActionResult GetInfo()
        {
            try
            {
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var assembly = Assembly.GetExecutingAssembly();

                var info = new
                {
                    service = new
                    {
                        name = ServiceName,
                        version = _configuration.GetSection("version").Value ?? UnknownValue,
                        build = assembly.GetName().Version?.ToString() ?? UnknownValue,
                        environment = _env.EnvironmentName
                    },
                    runtime = new
                    {
                        framework = RuntimeInformation.FrameworkDescription,
                        architecture = RuntimeInformation.OSArchitecture.ToString(),
                        processArchitecture = RuntimeInformation.ProcessArchitecture.ToString(),
                        dotnetVersion = Environment.Version.ToString()
                    },
                    process = new
                    {
                        name = process.ProcessName,
                        startTime = process.StartTime.ToUniversalTime(),
                        uptime = DateTime.UtcNow - process.StartTime.ToUniversalTime()
                    },
                    configuration = new
                    {
                        awsRegion = _configuration.GetSection("awsSettings:region").Value,
                        useLocalStack = _configuration.GetSection("awsSettings:useLocalStack").Value,
                        batchProcessingEnabled = _configuration.GetSection("batchProcessingSettings:enableBatchProcessing").Value,
                        batchSize = _configuration.GetSection("batchProcessingSettings:batchSize").Value,
                        healthCheckEnabled = _configuration.GetSection("healthCheckSettings:enabled").Value
                    },
                    timestamp = DateTime.UtcNow
                };

                return Ok(info);
            }
            catch (Exception)
            {
                return StatusCode(500, new { error = "Internal server error", timestamp = DateTime.UtcNow });
            }
        }

        [HttpGet("status")]
        public IActionResult GetStatus()
        {
            try
            {
                var overallStatus = _queueOrchestrator.OverallStatus;
                var statusString = overallStatus.ToLowerString();
                var activeConsumers = _queueOrchestrator.ActiveConsumers.Count;
                var configuredQueues = _queueOrchestrator.ConfiguredQueueCount;
                var healthyConsumers = _queueOrchestrator.HealthyConsumerCount;
                var unhealthyConsumers = activeConsumers - healthyConsumers;
                var isServiceHealthy = _queueOrchestrator.IsHealthy;

                var status = new
                {
                    service = ServiceName,
                    status = statusString,
                    consumers = new
                    {
                        active = activeConsumers,
                        configured = configuredQueues,
                        healthy = healthyConsumers,
                        unhealthy = unhealthyConsumers,
                        failed = _queueOrchestrator.FailedConsumerCount,
                        overall_healthy = isServiceHealthy
                    },
                    configuration = new
                    {
                        environment = _env.EnvironmentName,
                        awsRegion = _configuration.GetSection("awsSettings:region").Value,
                        useLocalStack = bool.TryParse(_configuration.GetSection("awsSettings:useLocalStack").Value, out var localStack) && localStack,
                        batchProcessing = bool.TryParse(_configuration.GetSection("batchProcessingSettings:enableBatchProcessing").Value, out var batch) && batch,
                        healthChecks = bool.TryParse(_configuration.GetSection("healthCheckSettings:enabled").Value, out var health) && health
                    },
                    timestamp = DateTime.UtcNow
                };

                // Return appropriate HTTP status code based on health
                return overallStatus switch
                {
                    HealthStatus.Unhealthy => StatusCode(503, status), // Service Unavailable
                    HealthStatus.Degraded => StatusCode(200, status), // OK but with warnings
                    HealthStatus.Healthy => Ok(status), // Healthy
                    _ => Ok(status)
                };
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { status = "error", error = ex.Message, timestamp = DateTime.UtcNow });
            }
        }
    }
}
