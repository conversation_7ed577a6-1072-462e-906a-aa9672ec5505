# SQSLibrary

Core library providing SQS message consumption, S3 file downloading, and batch processing for LogEvent XML files.

## Overview

SQSLibrary is the core logic of the SQS Consumer Service, providing:

- **SQS Message Processing**: Polling and consuming messages from AWS SQS queues
- **S3 Event Parsing**: Extracting S3 object information from SQS event notifications
- **S3 File Downloading**: Parallel downloading of LogEvent XML files from S3
- **Batch Processing**:  Ordering and batching of LogEvents for optimal performance
- **Health Monitoring**: Collector API health checking with circuit breaker pattern
- **Error Handling**: Retry logic and graceful error handling

## Key Components

### SQSManager
Main orchestrator that coordinates the entire message processing pipeline.

**Key Features:**
- Polls SQS queues for new messages
- Manages message lifecycle (receive, process, delete)
- Coordinates S3 downloads and batch processing
- Handles errors and retries

### S3EventParser
Parses S3 event notifications from SQS messages and extracts S3 object information.

**Supported Formats:**
- Direct S3 event notifications
- SNS-wrapped S3 events (for LocalStack compatibility)
- Multiple S3 records per message

### S3FileDownloader
Downloads LogEvent XML files from S3 with optimized parallel processing.

**Features:**
- Configurable concurrent downloads
- Exponential backoff retry logic
- Timeout handling
- Memory-efficient streaming

### BatchProcessor
 Orders and batches LogEvents for optimal processing performance.

**Ordering Logic:**
1. **Primary**: Timestamp (earliest first)
2. **Secondary**: Call Identifier (alphabetical)

**Batch Triggers:**
- Maximum batch size reached
- Timeout elapsed
- Manual trigger

### LogEvent
Represents a parsed LogEvent with metadata for tracking and processing.

**Properties:**
- Timestamp, Agency, Agent, Call/Incident Identifiers
- Event Type (StartCall, Media, EndMedia, EndCall)
- Original XML content
- SQS tracking information

### CollectorHealthChecker
Monitors the health of the Collector API and implements circuit breaker pattern.

**Features:**
- Periodic health checks with configurable intervals
- Circuit breaker pattern with consecutive failure thresholds
- Non-blocking health monitoring
- Automatic recovery detection
- Thread-safe concurrent access

**Health States:**
- **Healthy**: API responding successfully, processing continues
- **Unhealthy**: Multiple consecutive failures, processing paused
- **Recovery**: Transitioning from unhealthy to healthy state

## Configuration

### Configuration Structure

The library supports the following configuration settings:

#### AWS Settings
```json
{
  "awsSettings": {
    "region": "us-east-1",
    "useLocalStack": false,
    "serviceUrl": "http://localhost:4566",
    "accessKey": "your-access-key",
    "secretKey": "your-secret-key"
  }
}
```

#### SQS Settings
```json
{
  "sqsSettings": {
    "maxMessages": 10,
    "waitTimeSeconds": 20,
    "visibilityTimeoutSeconds": 30
  }
}
```

#### S3 Settings
```json
{
  "s3Settings": {
    "downloadTimeoutSeconds": 30,
    "downloadRetryAttempts": 3
  }
}
```

#### Batch Processing Settings
```json
{
  "batchProcessingSettings": {
    "enableBatchProcessing": true,
    "batchSize": 50,
    "batchTimeoutMs": 5000,
    "enableParallelS3Downloads": true,
    "maxConcurrentS3Downloads": 10
  }
}
```

#### Health Check Settings
```json
{
  "healthCheckSettings": {
    "enabled": true,
    "intervalSeconds": 30,
    "timeoutSeconds": 10,
    "maxConsecutiveFailures": 3
  }
}
```

#### Collector API Settings
```json
{
  "collectorAPISettings": {
    "baseUrl": "https://collector-api.example.com",
    "eventsEndpoint": "/api/events/{clientCode}",
    "healthEndpoint": "/api/health",
    "userId": "your-user-id",
    "apiKey": "your-api-key",
    "timeoutSeconds": 30
  }
}
```

#### QueueDefinition
```json
{
  "clientQueues": [
    {
      "clientCode": "CLIENT001",
      "queueName": "client001-events",
      "queueUrl": "https://sqs.us-east-1.amazonaws.com/123456789012/client001-events"
    }
  ]
}
```

## Error Handling

The library implements comprehensive error handling:

- **S3 Download Failures**: Exponential backoff with configurable retry attempts
- **Parse Errors**: Graceful handling of malformed XML
- **Network Issues**: Timeout handling and connection retry logic
- **Batch Processing**: Partial batch processing on individual event failures

## Performance Optimizations

- **Parallel S3 Downloads**: Configurable concurrency with semaphore-based throttling
- **Memory Efficiency**: Streaming downloads and minimal object allocation
- **Connection Pooling**: Reuses HTTP connections for better performance

## Testing

The library includes **56 comprehensive unit tests** with complete isolation:

### Test Coverage
- **LogEvent Parsing** (8 tests): XML validation, timezone handling, error scenarios, multiple event types
- **S3EventParser** (17 tests): Event parsing, SNS unwrapping, malformed data handling, edge cases
- **BatchProcessor** (13 tests): Ordering logic, batch triggers, null safety, performance
- **CollectorHealthChecker** (15 tests): Health monitoring, circuit breaker, recovery, concurrency

### Testing Principles
- **Complete Isolation**: All external dependencies mocked (HTTP clients, AWS services)
- **Deterministic Results**: No real network calls or timing dependencies
- **Fast Execution**: All tests complete in under 5 seconds
- **Edge Case Coverage**: Malformed data, network errors, timeout scenarios

### Test Categories
- **Happy Path**: Normal operation scenarios
- **Error Handling**: Network failures, malformed data, timeouts
- **Edge Cases**: Null inputs, empty collections, boundary conditions
- **Concurrency**: Thread safety and concurrent access patterns

Run tests with:
```bash
dotnet test SQSLibrary.Tests --verbosity normal
```

View test coverage:
```bash
dotnet test SQSLibrary.Tests --collect:"XPlat Code Coverage"
```

## Dependencies

- **AWS SDK**: SQS and S3 client libraries
- **Newtonsoft.Json**: JSON parsing for S3 events
- **Serilog**: Structured logging
