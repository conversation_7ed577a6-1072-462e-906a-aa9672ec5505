using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SQSConsumerService.Services;
using SQSConsumerService.Code;
using SQSLibrary;
using SQSLibrary.Configuration;

namespace SQSConsumerService.Tests
{
    public class QueueOrchestratorTests : IDisposable
    {
        private readonly Mock<ILogger<QueueOrchestrator>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<ISharedHealthCheckerService> _mockHealthChecker;
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<IConfigurationChangeNotificationService> _mockNotificationService;
        private readonly QueueOrchestrator _orchestrator;
        private readonly QueueDefinition[] _testQueues;

        // Configuration objects
        private readonly AwsSettings _awsSettings;
        private readonly SQSSettings _sqsSettings;
        private readonly S3Settings _s3Settings;
        private readonly BatchProcessingSettings _batchSettings;
        private readonly HealthCheckSettings _healthSettings;
        private readonly CollectorAPISettings _collectorAPISettings;

        public QueueOrchestratorTests()
        {
            _mockLogger = new Mock<ILogger<QueueOrchestrator>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockHealthChecker = new Mock<ISharedHealthCheckerService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockNotificationService = new Mock<IConfigurationChangeNotificationService>();

            // Setup test configuration objects
            _awsSettings = new AwsSettings
            {
                Region = "us-east-1",
                UseLocalStack = false
            };

            _sqsSettings = new SQSSettings
            {
                MaxMessages = 1,
                WaitTimeSeconds = 20,
                VisibilityTimeoutSeconds = 30
            };

            _s3Settings = new S3Settings
            {
                DownloadTimeoutSeconds = 30,
                DownloadRetryAttempts = 3
            };

            _batchSettings = new BatchProcessingSettings
            {
                EnableBatchProcessing = false,
                BatchSize = 50,
                BatchTimeoutMs = 5000
            };

            _healthSettings = new HealthCheckSettings
            {
                Enabled = true,
                IntervalSeconds = 30,
                TimeoutSeconds = 10,
                MaxConsecutiveFailures = 3
            };

            _collectorAPISettings = new CollectorAPISettings
            {
                BaseUrl = "https://test-api.example.com",
                EventsEndpoint = "/events",
                HealthEndpoint = "/health"
            };

            // Setup test queues
            _testQueues = new[]
            {
                new QueueDefinition
                {
                    ClientCode = "TEST001",
                    QueueName = "test-queue-1",
                    QueueUrl = "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue-1"
                },
                new QueueDefinition
                {
                    ClientCode = "TEST002",
                    QueueName = "test-queue-2",
                    QueueUrl = "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue-2"
                }
            };

            // Setup service provider using GetService instead of GetRequiredService
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISharedHealthCheckerService)))
                .Returns(_mockHealthChecker.Object);

            // Setup configuration to return test queues
            SetupMockConfiguration();

            _orchestrator = new QueueOrchestrator(
                _mockLogger.Object,
                _awsSettings,
                _sqsSettings,
                _s3Settings,
                _batchSettings,
                _healthSettings,
                _collectorAPISettings,
                _mockNotificationService.Object,
                _mockConfiguration.Object);
        }

        /// <summary>
        /// Sets up the mock configuration to return test queue definitions
        /// </summary>
        private void SetupMockConfiguration()
        {
            var mockClientQueuesSection = new Mock<IConfigurationSection>();
            var mockQueueSections = new List<IConfigurationSection>();

            // Create mock sections for each test queue
            for (int i = 0; i < _testQueues.Length; i++)
            {
                var mockQueueSection = new Mock<IConfigurationSection>();
                mockQueueSection.Setup(s => s.Key).Returns(i.ToString());

                // Setup individual property access instead of Bind
                mockQueueSection.Setup(s => s["ClientCode"]).Returns(_testQueues[i].ClientCode);
                mockQueueSection.Setup(s => s["QueueName"]).Returns(_testQueues[i].QueueName);
                mockQueueSection.Setup(s => s["QueueUrl"]).Returns(_testQueues[i].QueueUrl);
                mockQueueSection.Setup(s => s["Region"]).Returns(_testQueues[i].Region);

                mockQueueSections.Add(mockQueueSection.Object);
            }

            // Setup the section to return children and indicate it exists
            mockClientQueuesSection.Setup(s => s.GetChildren()).Returns(mockQueueSections);
            mockClientQueuesSection.Setup(s => s.Value).Returns((string)null); // Indicates section exists

            _mockConfiguration.Setup(c => c.GetSection("ClientQueues"))
                .Returns(mockClientQueuesSection.Object);
        }

        public void Dispose()
        {
            _orchestrator?.Dispose();
        }

        [Fact]
        public async Task InitializeAsync_ShouldLoadConfigurationAndQueues()
        {
            // Act
            await _orchestrator.InitializeAsync(_mockServiceProvider.Object);

            // Assert
            Assert.Equal(2, _orchestrator.ConfiguredQueueCount);
            _mockConfiguration.Verify(c => c.GetSection("ClientQueues"), Times.AtLeastOnce);
            _mockHealthChecker.Verify(hc => hc.Initialize(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<TimeSpan>(), It.IsAny<int>()), Times.Once);
        }

        [Fact]
        public void InitializeAsync_CalledTwice_ShouldNotReinitialize()
        {
            // Act
            _orchestrator.InitializeAsync(_mockServiceProvider.Object).Wait();
            _orchestrator.InitializeAsync(_mockServiceProvider.Object).Wait();

            // Assert - Configuration should only be loaded once during initialization
            _mockConfiguration.Verify(c => c.GetSection("ClientQueues"), Times.Once);
        }

        [Fact]
        public async Task StartConsumersAsync_WithoutInitialization_ShouldThrowException()
        {
            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _orchestrator.StartConsumersAsync());
            
            Assert.Contains("must be initialized", ex.Message);
        }

        [Fact]
        public async Task ValidateQueuesForTestingAsync_WithoutInitialization_ShouldThrowException()
        {
            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _orchestrator.ValidateQueuesForTestingAsync());

            Assert.Contains("must be initialized", ex.Message);
        }

        [Fact]
        public void OverallStatus_WithNoConsumers_ShouldReturnUnhealthy()
        {
            // Act
            var status = _orchestrator.OverallStatus;

            // Assert
            Assert.Equal(HealthStatus.Unhealthy, status);
        }

        [Fact]
        public void Constructor_ShouldSubscribeToConfigurationChangeNotifications()
        {
            // Assert
            _mockNotificationService.Verify(
                ns => ns.Subscribe(It.IsAny<EventHandler<ConfigurationChangedEventArgs>>()),
                Times.Once);
        }

        [Fact]
        public async Task CleanupAsync_ShouldUnsubscribeFromConfigurationChangeNotifications()
        {
            // Act
            await _orchestrator.CleanupAsync();

            // Assert
            _mockNotificationService.Verify(
                ns => ns.Unsubscribe(It.IsAny<EventHandler<ConfigurationChangedEventArgs>>()),
                Times.Once);
        }

        [Fact]
        public void IsHealthy_WithNoConsumers_ShouldReturnFalse()
        {
            // Act
            var isHealthy = _orchestrator.IsHealthy;

            // Assert
            Assert.False(isHealthy);
        }

        [Fact]
        public void GetHealthDetails_ShouldReturnValidHealthObject()
        {
            // Act
            var healthDetails = _orchestrator.GetHealthDetails();

            // Assert
            Assert.NotNull(healthDetails);
            
            // Use reflection to check properties since it's an anonymous object
            var healthType = healthDetails.GetType();
            var overallStatusProp = healthType.GetProperty("OverallStatus");
            var isHealthyProp = healthType.GetProperty("IsHealthy");
            var configuredQueuesProp = healthType.GetProperty("ConfiguredQueues");
            
            Assert.NotNull(overallStatusProp);
            Assert.NotNull(isHealthyProp);
            Assert.NotNull(configuredQueuesProp);
        }

        [Fact]
        public async Task CleanupAsync_ShouldStopConsumersAndHealthChecker()
        {
            // Arrange
            await _orchestrator.InitializeAsync(_mockServiceProvider.Object);

            // Act
            await _orchestrator.CleanupAsync();

            // Assert
            _mockHealthChecker.Verify(hc => hc.Stop(), Times.Once);
        }

        [Fact]
        public void Dispose_ShouldCallCleanup()
        {
            // Act
            _orchestrator.Dispose();

            // Assert - Should not throw and should complete cleanup
            Assert.True(true); // Dispose completed without exceptions
        }


    }
}
