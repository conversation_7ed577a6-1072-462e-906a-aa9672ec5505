using System;

namespace SQSLibrary
{

    /// <summary>
    /// Defines the Queue for SQS usage
    /// </summary>
    public class QueueDefinition
    {
        /// <summary>
        /// Unique client code as matched to Collector configuration to match data to target process
        /// </summary>
        public string ClientCode { get; set; }
        
        private string _QueueName = string.Empty;
        /// <summary>
        /// Target QueueName
        /// </summary>
        public string QueueName
        {
            get
            {
                return _QueueName;
            }
            set
            {
                _QueueName = Encryption.Decrypt(value);
            }
        }
        
        private string _QueueUrl = string.Empty;
        /// <summary>
        /// Full SQS Queue URL
        /// </summary>
        public string QueueUrl
        {
            get
            {
                return _QueueUrl;
            }
            set
            {
                _QueueUrl = Encryption.Decrypt(value);
            }
        }

        /// <summary>
        /// AWS Region for the SQS queue
        /// Default is us-east-1
        /// </summary>
        public string Region { get; set; } = "us-east-1";

        /// <summary>
        /// To String overwrite for easy debugging infromation
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{QueueName}:{QueueUrl}::{ClientCode}";
        }
    }

}
