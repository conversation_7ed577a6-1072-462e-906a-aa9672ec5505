@echo off
echo -------------------------------------------------------------------
echo                S3 LogEvent Test Data Generator
echo -------------------------------------------------------------------

REM Set AWS credentials for LocalStack
set AWS_ACCESS_KEY_ID=test
set AWS_SECRET_ACCESS_KEY=test
set AWS_DEFAULT_REGION=us-east-1
set AWS_ENDPOINT_URL=http://localhost:4566

set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=local

set WASHINGTION_BUCKET=%ENVIRONMENT%-washingtoncounty-mn-datalake-raw-01
set FLAGLER_BUCKET=%ENVIRONMENT%-flaglercounty-mn-datalake-raw-01
set TEMP_DIR=%TEMP%\test-logevents-%RANDOM%

echo Environment: %ENVIRONMENT%
echo S3 Buckets: %WASHINGTION_BUCKET% and %FLAGLER_BUCKET%
echo Temp Directory: %TEMP_DIR%
echo.

REM Create temporary directory
mkdir "%TEMP_DIR%" 2>nul

REM Generate sample LogEvent XML files
echo --- Generating Sample LogEvent XML Files ---

REM Use simple timestamp and call ID
set TIMESTAMP=2025-06-03T20:30:00.000Z
set CALL_ID=TEST_CALL_%RANDOM%
set YEAR=2025
set MONTH=06
set DAY=03

echo Generating call scenario: %CALL_ID%
echo Timestamp: %TIMESTAMP%
echo.

REM Generate LogEvent 1: StartCall
set FILENAME1=logevent_%CALL_ID%_001.xml
echo Creating LogEvent 1: StartCall
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<LogEvent xmlns="http://solacom.com/Logging"^>
echo ^<timestamp^>%TIMESTAMP%^</timestamp^>
echo ^<agencyOrElement^>Porsche_A^</agencyOrElement^>
echo ^<agent^>.^</agent^>
echo ^<callIdentifier^>_CI_%CALL_ID%^</callIdentifier^>
echo ^<incidentIdentifier^>_II_%CALL_ID%^</incidentIdentifier^>
echo ^<eventType^>StartCall^</eventType^>
echo ^<startCall^>
echo ^<header^>
echo ^<![CDATA[ INVITE urn:responder1.qc.psap.ng.911bell.ca SIP/2.0 Via: SIP/2.0/TCP ************:5060 ]]^>
echo ^</header^>
echo ^<location^>.^</location^>
echo ^<mediaLabel^>_ML_18BC96298E0C00000003@Porsche^</mediaLabel^>
echo ^<incomingCallPolicy^>23_Ad-HocResponder^</incomingCallPolicy^>
echo ^<callType^>SR911^</callType^>
echo ^<signallingType^>VOIP^</signallingType^>
echo ^<circuit^>30/02/00/0003^</circuit^>
echo ^<circuitId^>126091267^</circuitId^>
echo ^<trunkGroupId^>100^</trunkGroupId^>
echo ^<ani^>e5ef0a65502649a7aa0a2bc77a6840da^</ani^>
echo ^<aniDomain^>focus.core.ng911.bell.com^</aniDomain^>
echo ^<dnis^>sos^</dnis^>
echo ^<dnisDomain^>responder1.qc.psap.ng.911bell.ca^</dnisDomain^>
echo ^<pani^>e5ef0a65502649a7aa0a2bc77a6840da^</pani^>
echo ^<esrn^>.^</esrn^>
echo ^<callerName^>Focus^</callerName^>
echo ^<concurrentCalls^>1^</concurrentCalls^>
echo ^</startCall^>
echo ^</LogEvent^>
) > "%TEMP_DIR%\%FILENAME1%"

REM Generate LogEvent 2: Media
set FILENAME2=logevent_%CALL_ID%_002.xml
echo Creating LogEvent 2: Media
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<LogEvent xmlns="http://solacom.com/Logging"^>
echo ^<timestamp^>%TIMESTAMP%^</timestamp^>
echo ^<agencyOrElement^>Porsche_A^</agencyOrElement^>
echo ^<agent^>.^</agent^>
echo ^<callIdentifier^>_CI_%CALL_ID%^</callIdentifier^>
echo ^<incidentIdentifier^>_II_%CALL_ID%^</incidentIdentifier^>
echo ^<eventType^>Media^</eventType^>
echo ^<media^>
echo ^<mediaLabel^>_ML_18BC96298E0C00000003@Porsche^</mediaLabel^>
echo ^<udp^>v=0  o=AudiocodesGW 2047710930 1433415697 IN IP4 *************  s=Phone-Call  c=IN IP4 *************  t=0 0  m=audio 7780 RTP/AVP 0 101  a=ptime:20  a=sendrecv  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=fmtp:101 0-15  ^</udp^>
echo ^</media^>
echo ^</LogEvent^>
) > "%TEMP_DIR%\%FILENAME2%"

REM Generate LogEvent 3: EndMedia
set FILENAME3=logevent_%CALL_ID%_003.xml
echo Creating LogEvent 3: EndMedia
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<LogEvent xmlns="http://solacom.com/Logging"^>
echo ^<timestamp^>%TIMESTAMP%^</timestamp^>
echo ^<agencyOrElement^>Porsche_A^</agencyOrElement^>
echo ^<agent^>.^</agent^>
echo ^<callIdentifier^>_CI_%CALL_ID%^</callIdentifier^>
echo ^<incidentIdentifier^>_II_%CALL_ID%^</incidentIdentifier^>
echo ^<eventType^>EndMedia^</eventType^>
echo ^<endMedia^>
echo ^<mediaLabel^>_ML_18BC96298E0C00000003@Porsche^</mediaLabel^>
echo ^<responseCode^>16^</responseCode^>
echo ^<disconnectReason^>^</disconnectReason^>
echo ^<voiceQOS^>
echo ^<mediaIpSourceAddr^>************^</mediaIpSourceAddr^> 
echo ^<mediaIpDestAddr^>*********^</mediaIpDestAddr^> 
echo ^<mediaUdpRtpSourcePort^>22872^</mediaUdpRtpSourcePort^> 
echo ^<mediaUdpRtpDestPort^>20028^</mediaUdpRtpDestPort^> 
echo ^<mediaNumOfIpPktRxed^>65535^</mediaNumOfIpPktRxed^> 
echo ^<mediaNumOfIpPktTxed^>65535^</mediaNumOfIpPktTxed^>
echo ^<mediaNumOfIpErroredPktRxed^>65535^</mediaNumOfIpErroredPktRxed^>
echo ^<mediaNumOfRtpPktRxed^>0^</mediaNumOfRtpPktRxed^> 
echo ^<mediaNumOfRtpPktTxed^>3781^</mediaNumOfRtpPktTxed^> 
echo ^<mediaNumOfRtpPktLost^>0^</mediaNumOfRtpPktLost^>
echo ^<mediaNumOfRtpPktDiscarded^>0^</mediaNumOfRtpPktDiscarded^>
echo ^<mediaRtpJitter^>0^</mediaRtpJitter^>
echo ^<mediaRtpLatency^>0^</mediaRtpLatency^>
echo ^<mediaNumOfRtcpPktRxed^>0^</mediaNumOfRtcpPktRxed^> 
echo ^<mediaNumOfRtcpPktTxed^>30^</mediaNumOfRtcpPktTxed^> 
echo ^<mediaFarEndPacketLostPercentage^>0^</mediaFarEndPacketLostPercentage^> 
echo ^<mediaFarEndCumulativePacketLost^>0^</mediaFarEndCumulativePacketLost^> 
echo ^<mediaFarEndInterarrivalJitter^>0^</mediaFarEndInterarrivalJitter^> 
echo ^</voiceQOS^>
echo ^</endMedia^>
echo ^</LogEvent^>
) > "%TEMP_DIR%\%FILENAME3%"

REM Generate LogEvent 4: EndCall
set FILENAME4=logevent_%CALL_ID%_004.xml
echo Creating LogEvent 4: EndCall
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<LogEvent xmlns="http://solacom.com/Logging"^>
echo ^<timestamp^>%TIMESTAMP%^</timestamp^>
echo ^<agencyOrElement^>Porsche_A^</agencyOrElement^>
echo ^<agent^>.^</agent^>
echo ^<callIdentifier^>_CI_%CALL_ID%^</callIdentifier^>
echo ^<incidentIdentifier^>_II_%CALL_ID%^</incidentIdentifier^>
echo ^<eventType^>EndCall^</eventType^>
echo ^<endCall^>
echo ^<responseCode^>16^</responseCode^>
echo ^<duration^>120^</duration^>
echo ^<callReplaced^>No^</callReplaced^>
echo ^</endCall^>
echo ^</LogEvent^>
) > "%TEMP_DIR%\%FILENAME4%"

REM Generate LogEvent 5: AgentAvailable
set FILENAME5=logevent_%NULL_CALL_ID%_005.xml
echo Creating LogEvent 5: AgentAvailable
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<LogEvent xmlns="http://solacom.com/Logging"^>
echo ^<timestamp^>2025-04-29T23:05:17.913Z^</timestamp^>
echo ^<agencyOrElement^>NicholasCountyWV^</agencyOrElement^>
echo ^<agent^>CKinder^</agent^>
echo ^<eventType^>AgentAvailable^</eventType^>
echo ^<agentAvailable^>
echo ^<mediaLabel^>.^</mediaLabel^>
echo ^<uri^>tel:+5551010003^</uri^>
echo ^<agentRole^>Dispatcher^</agentRole^>
echo ^<tenantGroup^>NicholasCountyWV^</tenantGroup^>
echo ^<operatorId^>3^</operatorId^>
echo ^<workstation^>OP3^</workstation^>
echo ^<busiedOutAction^>Manual^</busiedOutAction^>
echo ^</agentAvailable^>
echo ^</LogEvent^>
) > "%TEMP_DIR%\%FILENAME5%"
echo.
echo --- Uploading LogEvent Files to S3 ---

REM Upload files to S3 using curl to avoid AWS CLI trailer header issues
echo Uploading %FILENAME1%
curl -X PUT "http://localhost:4566/%WASHINGTION_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME1%" --data-binary @"%TEMP_DIR%\%FILENAME1%" -H "Content-Type: application/xml" -s
curl -X PUT "http://localhost:4566/%FLAGLER_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME1%" --data-binary @"%TEMP_DIR%\%FILENAME1%" -H "Content-Type: application/xml" -s

if %errorlevel% equ 0 (echo Uploaded: %FILENAME1%) else (echo Failed: %FILENAME1%)

timeout /t 2 /nobreak >nul

echo Uploading %FILENAME2%
curl -X PUT "http://localhost:4566/%WASHINGTION_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME2%" --data-binary @"%TEMP_DIR%\%FILENAME2%" -H "Content-Type: application/xml" -s
curl -X PUT "http://localhost:4566/%FLAGLER_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME2%" --data-binary @"%TEMP_DIR%\%FILENAME2%" -H "Content-Type: application/xml" -s
if %errorlevel% equ 0 (echo Uploaded: %FILENAME2%) else (echo Failed: %FILENAME2%)

timeout /t 2 /nobreak >nul

echo Uploading %FILENAME3%
curl -X PUT "http://localhost:4566/%WASHINGTION_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME3%" --data-binary @"%TEMP_DIR%\%FILENAME3%" -H "Content-Type: application/xml" -s
curl -X PUT "http://localhost:4566/%FLAGLER_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME3%" --data-binary @"%TEMP_DIR%\%FILENAME3%" -H "Content-Type: application/xml" -s
if %errorlevel% equ 0 (echo Uploaded: %FILENAME3%) else (echo Failed: %FILENAME3%)

timeout /t 2 /nobreak >nul

echo Uploading %FILENAME4%
curl -X PUT "http://localhost:4566/%WASHINGTION_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME4%" --data-binary @"%TEMP_DIR%\%FILENAME4%" -H "Content-Type: application/xml" -s
curl -X PUT "http://localhost:4566/%FLAGLER_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME4%" --data-binary @"%TEMP_DIR%\%FILENAME4%" -H "Content-Type: application/xml" -s
if %errorlevel% equ 0 (echo Uploaded: %FILENAME4%) else (echo Failed: %FILENAME4%)

timeout /t 2 /nobreak >nul

echo Uploading %FILENAME5%
curl -X PUT "http://localhost:4566/%WASHINGTION_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME5%" --data-binary @"%TEMP_DIR%\%FILENAME5%" -H "Content-Type: application/xml" -s
curl -X PUT "http://localhost:4566/%FLAGLER_BUCKET%/CHE/SOLACOM/%YEAR%/%MONTH%/%DAY%/%FILENAME5%" --data-binary @"%TEMP_DIR%\%FILENAME5%" -H "Content-Type: application/xml" -s
if %errorlevel% equ 0 (echo Uploaded: %FILENAME5%) else (echo Failed: %FILENAME5%)


echo.
echo --- Waiting for S3 -> SNS -> SQS Processing ---
echo Waiting 10 seconds for event processing...
timeout /t 10 /nobreak >nul

echo.
echo --- Checking SQS Queue for Messages ---
set QUEUE_NAME=%ENVIRONMENT%-washingtoncounty-logevents-queue
echo Checking queue: %QUEUE_NAME%

aws sqs get-queue-attributes --queue-url "http://localhost:4566/000000000000/%QUEUE_NAME%" --attribute-names ApproximateNumberOfMessages --endpoint-url=%AWS_ENDPOINT_URL% --query "Attributes.ApproximateNumberOfMessages" --output text
if %errorlevel% equ 0 (echo Queue checked successfully) else (echo Failed to check queue)

echo.
echo --- Sample SQS Message ---
aws sqs receive-message --queue-url "http://localhost:4566/000000000000/%QUEUE_NAME%" --max-number-of-messages 1 --endpoint-url=%AWS_ENDPOINT_URL% --query "Messages[0].Body" --output text
if %errorlevel% equ 0 (echo Sample message retrieved) else (echo No messages available)

REM Clean up temporary files
echo.
echo --- Cleanup ---
rmdir /s /q "%TEMP_DIR%" 2>nul
echo Cleaned up temporary files

echo.
echo -------------------------------------------------------------------
echo    Test Data Generation Complete!
echo -------------------------------------------------------------------
echo.
echo Generated and uploaded 5 LogEvent XML files
echo Event types: StartCall, Media, EndMedia, EndCall, AgentAvailable
echo These should now be available as S3 event notifications in SQS.
echo.
echo Next steps:
echo 1. Unit tests: dotnet test SQSLibrary.Tests
echo 2. Integration tests: dotnet test SQSConsumerService.IntegrationTests
echo 3. Run consumer: dotnet run --project SQSConsumerService/SQSConsumerService.csproj
echo.
echo -------------------------------------------------------------------
