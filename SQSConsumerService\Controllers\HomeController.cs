using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using SQSConsumerService.Code;
using Microsoft.AspNetCore.Authorization;


namespace SQSConsumerService.Controllers
{
    public class HomeController : Controller
    {
        private readonly IWebHostEnvironment _env;
        private readonly IConfiguration _config;
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger, IWebHostEnvironment env, IConfiguration configuration)
        {
            _env = env;
            _config = configuration;
            _logger = logger;
        }
        // GET: /<controller>/
        public IActionResult Index()
        {
            var data = new AboutApiData
            {
                Environment = _env.EnvironmentName,
                Name = "SQS Consumer",
                Version = _config.GetSection("version").Value,
                Build = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
            };
            return View(data);
        }

        [HttpPost]
        [Route("encrypt")]
        [AllowAnonymous]
        public async Task<string> GetEncrypt()
        {
            string payload = string.Empty;

            using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
            {
                payload = await reader.ReadToEndAsync();
            }

            string rtnEncryption = SQSLibrary.Encryption.Encrypt(payload);
            _logger.LogInformation("Encryption Event called. Result: [{EncryptionResult}]", rtnEncryption);

            return rtnEncryption;
        }
        
        [HttpPost]
        [Route("decrypt")]
        [AllowAnonymous]
        public async Task<string> GetDecrypt()
        {
            string payload = string.Empty;

            using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
            {
                payload = await reader.ReadToEndAsync();
            }

            string rtnDecryption = SQSLibrary.Encryption.Decrypt(payload);
            _logger.LogInformation("Decryption Event called. Result: [{DecryptionResult}]", rtnDecryption);

            return rtnDecryption;
        }
    }
}
