using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Moq;
using Moq.Contrib.HttpClient;
using SQSLibrary.Services;

namespace SQSLibrary.Tests
{
    /// <summary>
    /// Unit tests for CollectorHealthChecker functionality
    /// These tests use mocked HTTP clients for complete isolation
    /// </summary>
    public class CollectorHealthCheckerTests : IDisposable
    {
        private readonly Mock<HttpMessageHandler> _mockHttpHandler;
        private readonly HttpClient _mockHttpClient;
        private readonly string _testHealthUrl = "http://test-collector.com/health";

        public CollectorHealthCheckerTests()
        {
            _mockHttpHandler = new Mock<HttpMessageHandler>();
            _mockHttpClient = new HttpClient(_mockHttpHandler.Object);
        }

        [Fact]
        public void Constructor_ValidParameters_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                httpClient: _mockHttpClient,
                startTimer: false); // Disable timer for tests

            // Assert
            healthChecker.Should().NotBeNull();
            // Starts as healthy
            healthChecker.IsHealthy.Should().BeTrue();
            healthChecker.ConsecutiveFailures.Should().Be(0);
        }

        [Fact]
        public async Task CheckHealthAsync_SuccessfulResponse_ShouldReturnTrue()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.OK, "Healthy");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act
            var result = await healthChecker.CheckHealthAsync();

            // Assert
            result.Should().BeTrue();
            healthChecker.IsHealthy.Should().BeTrue();
            healthChecker.ConsecutiveFailures.Should().Be(0);
        }

        [Fact]
        public async Task CheckHealthAsync_FailedResponse_ShouldReturnFalse()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.InternalServerError, "Unhealthy");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 1,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act
            var result = await healthChecker.CheckHealthAsync();

            // Assert
            result.Should().BeFalse();
            healthChecker.IsHealthy.Should().BeFalse();
            healthChecker.ConsecutiveFailures.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task CheckHealthAsync_ConcurrentCalls_ShouldNotBlock()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.OK, "Healthy");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act - Start multiple concurrent health checks
            var task1 = healthChecker.CheckHealthAsync();
            var task2 = healthChecker.CheckHealthAsync();
            var task3 = healthChecker.CheckHealthAsync();

            var results = await Task.WhenAll(task1, task2, task3);

            // Assert - All should complete without blocking
            results.Should().HaveCount(3);
            // Results should be consistent (all same value)
            results[0].Should().Be(results[1]);
            results[1].Should().Be(results[2]);
        }

        [Fact]
        public async Task WaitForHealthyAsync_WithTimeout_ShouldRespectTimeout()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.InternalServerError, "Unhealthy");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 1,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Force unhealthy state
            await healthChecker.CheckHealthAsync();

            // Act
            var startTime = DateTime.UtcNow;
            var result = await healthChecker.WaitForHealthyAsync(TimeSpan.FromSeconds(1));
            var elapsed = DateTime.UtcNow - startTime;

            // Assert
            result.Should().BeFalse();
            elapsed.Should().BeGreaterOrEqualTo(TimeSpan.FromSeconds(0.8));
            elapsed.Should().BeLessThan(TimeSpan.FromSeconds(2));
        }

        [Fact]
        public async Task WaitForHealthyAsync_WithCancellation_ShouldRespectCancellation()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.InternalServerError, "Unhealthy");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 1,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Force unhealthy state
            await healthChecker.CheckHealthAsync();

            using var cts = new CancellationTokenSource();

            // Act
            var waitTask = healthChecker.WaitForHealthyAsync(TimeSpan.FromMinutes(1), cts.Token);

            // Cancel after 1 second
            cts.CancelAfter(TimeSpan.FromSeconds(1));

            var result = await waitTask;

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void IsHealthy_InitialState_ShouldBeTrue()
        {
            // Arrange & Act
            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Assert
            healthChecker.IsHealthy.Should().BeTrue();
        }

        [Fact]
        public void ConsecutiveFailures_InitialState_ShouldBeZero()
        {
            // Arrange & Act
            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Assert
            healthChecker.ConsecutiveFailures.Should().Be(0);
        }

        [Fact]
        public void LastSuccessfulCheck_InitialState_ShouldBeRecent()
        {
            // Arrange
            var beforeCreation = DateTime.UtcNow;

            // Act
            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                httpClient: _mockHttpClient,
                startTimer: false);
            var afterCreation = DateTime.UtcNow;

            // Assert
            healthChecker.LastSuccessfulCheck.Should().BeOnOrAfter(beforeCreation);
            healthChecker.LastSuccessfulCheck.Should().BeOnOrBefore(afterCreation);
        }

        [Fact]
        public async Task CheckHealthAsync_AfterDispose_ShouldReturnFalse()
        {
            // Arrange
            var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                httpClient: _mockHttpClient,
                startTimer: false);
            healthChecker.Dispose();

            // Act
            var result = await healthChecker.CheckHealthAsync();

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void Dispose_ShouldNotThrow()
        {
            // Arrange
            var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act & Assert
            healthChecker.Invoking(h => h.Dispose()).Should().NotThrow();

            // Multiple dispose calls should not throw
            healthChecker.Invoking(h => h.Dispose()).Should().NotThrow();
        }

        [Fact]
        public async Task CheckHealthAsync_HttpTimeout_ShouldReturnFalse()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .Throws(new TaskCanceledException("Request timeout"));

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 1,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act
            var result = await healthChecker.CheckHealthAsync();

            // Assert
            result.Should().BeFalse();
            healthChecker.IsHealthy.Should().BeFalse();
        }

        [Fact]
        public async Task CheckHealthAsync_NetworkError_ShouldReturnFalse()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .Throws(new HttpRequestException("Network error"));

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 1,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act
            var result = await healthChecker.CheckHealthAsync();

            // Assert
            result.Should().BeFalse();
            healthChecker.IsHealthy.Should().BeFalse();
        }

        [Fact]
        public async Task CheckHealthAsync_MultipleFailures_ShouldMarkUnhealthy()
        {
            // Arrange
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.InternalServerError, "Error");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 2,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act - First failure should not mark as unhealthy
            var result1 = await healthChecker.CheckHealthAsync();

            // Assert - Still healthy after first failure
            result1.Should().BeFalse();
            healthChecker.IsHealthy.Should().BeTrue();
            healthChecker.ConsecutiveFailures.Should().Be(1);

            // Act - Second failure should mark as unhealthy
            var result2 = await healthChecker.CheckHealthAsync();

            // Assert - Now unhealthy
            result2.Should().BeFalse();
            healthChecker.IsHealthy.Should().BeFalse();
            healthChecker.ConsecutiveFailures.Should().Be(2);
        }

        [Fact]
        public async Task CheckHealthAsync_RecoveryAfterFailure_ShouldRestoreHealth()
        {
            // Arrange - First setup for failures
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.InternalServerError, "Error");

            using var healthChecker = new CollectorHealthChecker(
                _testHealthUrl,
                healthCheckInterval: TimeSpan.FromHours(1),
                maxConsecutiveFailures: 2,
                httpClient: _mockHttpClient,
                startTimer: false);

            // Act - Two failures to mark unhealthy
            await healthChecker.CheckHealthAsync();
            await healthChecker.CheckHealthAsync();

            // Assert - Should be unhealthy
            healthChecker.IsHealthy.Should().BeFalse();

            // Arrange - Setup for recovery
            _mockHttpHandler.Reset();
            _mockHttpHandler.SetupRequest(HttpMethod.Get, "http://test-collector.com/health")
                .ReturnsResponse(HttpStatusCode.OK, "Healthy");

            // Act - Recovery
            var result = await healthChecker.CheckHealthAsync();

            // Assert - Should be healthy again
            result.Should().BeTrue();
            healthChecker.IsHealthy.Should().BeTrue();
            healthChecker.ConsecutiveFailures.Should().Be(0);
        }

        public void Dispose()
        {
            _mockHttpClient?.Dispose();
        }
    }
}
