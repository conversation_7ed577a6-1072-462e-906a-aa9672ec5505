using Serilog;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace SQSLibrary
{
    /// <summary>
    /// Wrapper for the Pushing of events
    /// </summary>
    public static class PushEvent
    {
        // Static HttpClient to reuse connections
        private static readonly HttpClient _client = new HttpClient();
        
        /// <summary>
        /// Pushing the event to the collector end point
        /// </summary>
        /// <param name="collectorEndPoint">Full URL required, including client code </param>
        /// <param name="message">Message to be pushed</param>
        /// <returns>Success</returns>
        public static async Task<bool> ToCollectorAPI(string collectorEndPoint, string eventMessage)
        {
            try
            {
                using StringContent eventString = new StringContent(eventMessage, System.Text.Encoding.UTF8, "text/xml");
                HttpResponseMessage response = await _client.PostAsync(collectorEndPoint, eventString).ConfigureAwait(false);
                string responseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                
                if (response.IsSuccessStatusCode)
                {
                    Log.Logger.Debug("Pushed event to Collector API with status description: {ResponseContent}", responseContent);
                    return true;
                }
                else
                {
                    Log.Logger.Information("Failed to push event to Collector API with status code: {StatusCode}, description: {ResponseContent}",
                        (int)response.StatusCode, responseContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Warning(ex, "Exception occurred while pushing event to Collector API");
                return false;
            }
        }
    }
}

