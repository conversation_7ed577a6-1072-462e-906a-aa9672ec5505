<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
    <UserSecretsId>faec46ca-7dec-4964-9e83-c18e77c28750</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.7" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.400.44" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.400.44" />
    <PackageReference Include="Kralizek.Extensions.Configuration.AWSSecretsManager" Version="1.7.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="2.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.Enrichers.AspnetcoreHttpcontext" Version="1.1.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\SQSLibrary\SQSLibrary.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Content Update="Views\Home\Index.cshtml">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
