namespace SQSLibrary.Configuration
{
    /// <summary>
    /// Health check configuration settings
    /// </summary>
    public class HealthCheckSettings
    {
        /// <summary>
        /// Whether health checking is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Interval between health checks in seconds
        /// </summary>
        public int IntervalSeconds { get; set; } = 30;

        /// <summary>
        /// Timeout for health check requests in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 10;

        /// <summary>
        /// Maximum consecutive failures before marking as unhealthy
        /// </summary>
        public int MaxConsecutiveFailures { get; set; } = 3;
    }
}
