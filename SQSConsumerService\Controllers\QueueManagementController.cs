using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SQSConsumerService.Services;
using System;
using System.Threading.Tasks;

namespace SQSConsumerService.Controllers
{
    /// <summary>
    /// Controller for queue management operations including configuration refresh
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class QueueManagementController : ControllerBase
    {
        private readonly ILogger<QueueManagementController> _logger;
        private readonly IQueueOrchestrator _queueOrchestrator;

        /// <summary>
        /// Initializes a new instance of the QueueManagementController
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="queueOrchestrator">Queue orchestrator service</param>
        public QueueManagementController(ILogger<QueueManagementController> logger, IQueueOrchestrator queueOrchestrator)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _queueOrchestrator = queueOrchestrator ?? throw new ArgumentNullException(nameof(queueOrchestrator));
        }

        /// <summary>
        /// Refresh queue configurations and restart consumers if changes are detected
        /// This endpoint is private and should not be exposed publicly, it is intended for debugging and testing purposes only.
        /// </summary>
        /// <returns>Result indicating whether configuration changes were detected and applied</returns>
        [HttpPost("refresh")]
        private async Task<IActionResult> RefreshConfigurations()
        {
            try
            {
                _logger.LogInformation("Manual queue configuration refresh requested");

                var hasChanges = await _queueOrchestrator.RefreshQueueConfigurationsAsync();

                var result = new
                {
                    success = true,
                    changesDetected = hasChanges,
                    message = hasChanges
                        ? "Configuration changes detected and applied successfully"
                        : "No configuration changes detected",
                    note = "Automatic configuration refresh is also enabled via AWS Secrets Manager polling",
                    timestamp = DateTime.UtcNow,
                    activeConsumers = _queueOrchestrator.ActiveConsumers.Count,
                    configuredQueues = _queueOrchestrator.ConfiguredQueueCount
                };

                _logger.LogInformation("Manual queue configuration refresh completed. Changes applied: {HasChanges}", hasChanges);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Queue configuration refresh failed due to invalid operation");
                return BadRequest(new
                {
                    success = false,
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Queue configuration refresh failed");
                return StatusCode(500, new
                {
                    success = false,
                    error = "Internal server error during configuration refresh",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Get current queue configuration summary including health details
        /// This endpoint is private and should not be exposed publicly, it is intended for debugging and testing purposes only.
        /// </summary>
        /// <returns>Summary of current queue configurations without sensitive details</returns>
        [HttpGet("summary")]
        private IActionResult GetConfigurationSummary()
        {
            try
            {
                var summary = new
                {
                    configuredQueues = _queueOrchestrator.ConfiguredQueueCount,
                    activeConsumers = _queueOrchestrator.ActiveConsumers.Count,
                    healthyConsumers = _queueOrchestrator.HealthyConsumerCount,
                    failedConsumers = _queueOrchestrator.FailedConsumerCount,
                    overallStatus = _queueOrchestrator.OverallStatus.ToString().ToLower(),
                    isHealthy = _queueOrchestrator.IsHealthy,
                    automaticRefresh = new
                    {
                        enabled = true,
                        method = "AWS Secrets Manager auto-polling",
                        note = "Configuration changes are automatically detected and applied"
                    },
                    healthDetails = _queueOrchestrator.GetHealthDetails(),
                    timestamp = DateTime.UtcNow
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get configuration summary");
                return StatusCode(500, new
                {
                    error = "Internal server error",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Restart all consumers without checking for configuration changes
        /// This endpoint is private and should not be exposed publicly, it is intended for debugging and testing purposes only.
        /// </summary>
        /// <returns>Result of the restart operation</returns>
        [HttpPost("restart")]
        private async Task<IActionResult> RestartConsumers()
        {
            try
            {
                _logger.LogInformation("Consumer restart requested");

                await _queueOrchestrator.StopConsumersAsync();
                await _queueOrchestrator.StartConsumersAsync();

                var result = new
                {
                    success = true,
                    message = "All consumers restarted successfully",
                    timestamp = DateTime.UtcNow,
                    activeConsumers = _queueOrchestrator.ActiveConsumers.Count,
                    configuredQueues = _queueOrchestrator.ConfiguredQueueCount
                };

                _logger.LogInformation("Consumer restart completed successfully");
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Consumer restart failed due to invalid operation");
                return BadRequest(new 
                { 
                    success = false, 
                    error = ex.Message, 
                    timestamp = DateTime.UtcNow 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Consumer restart failed");
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "Internal server error during consumer restart", 
                    timestamp = DateTime.UtcNow 
                });
            }
        }
    }
}
