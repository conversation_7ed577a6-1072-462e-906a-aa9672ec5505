{"Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "SQSLibrary": "Information", "SQSConsumerService": "Information"}, "elasticsearchSettings": {"serilog": {"enableSSL": "true", "restrictedToMinimumLevel": "Information", "indexFormat": "application_log_smart_analytics-{0:yyyy.MM.dd}", "autoRegisterTemplate": false, "autoRegisterTemplateVersion": "ESv7"}}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "SQSLibrary": "Information", "SQSConsumerService": "Information"}}, "Properties": {"ApplicationName": "SQSConsumerService", "Instance": "", "Environment": ""}, "WriteTo": [{"Name": "File", "Args": {"restrictedToMinimumLevel": "Information", "path": "Logs/logs_.log", "rollingInterval": "Day", "retainedFileCountLimit": 31, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}]}, "awsSecretsManager": {"enabled": true, "fallbackToLocal": false, "pollingIntervalMinutes": 240}, "configurationChangeMonitor": {"enabled": true, "debounceDelayMs": 5000}, "awsSettings": {"region": "us-east-1", "useLocalStack": false}, "sqsSettings": {"maxMessages": 1, "waitTimeSeconds": 20, "visibilityTimeoutSeconds": 30}, "s3Settings": {"downloadTimeoutSeconds": 30, "downloadRetryAttempts": 3}, "batchProcessingSettings": {"enableBatchProcessing": true, "batchSize": 1, "batchTimeoutMs": 3000, "enableParallelS3Downloads": true, "maxConcurrentS3Downloads": 10}, "collectorAPISettings": {"baseUrl": "http://localhost:8888", "eventsEndpoint": "/api/events/{clientCode}", "healthEndpoint": "/api/health/ping", "userId": "14c/bpnwiR8rgjvUyzc6QHcnSztStAhgtUMBu0YxulgXqsi+mNgMB/fxPJy0U5wrXri7MBz40CqvJyvBX253/g==", "apiKey": "hJhLdy3acCKTavP325kvbTofFyIXKa7X4OO8cUThfqbyHr+wIaiIW9XPJS3eBIVGQYp/ZuDgPTuN3IFbOgclNA==", "timeoutSeconds": 30}, "healthCheckSettings": {"enabled": true, "intervalSeconds": 60, "timeoutSeconds": 10, "maxConsecutiveFailures": 3}, "AllowedHosts": "*", "version": "1.0.0"}