version: '3.8'

services:
  # LocalStack for local AWS services and setup localstack with the scripts
  localstack:
    container_name: localstack-s3-sqs
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"
      - "4510-4559:4510-4559"
    environment:
      - DEBUG=1
      - PERSISTENCE=1
      - SKIP_INFRA_DOWNLOADS=1
      - SERVICES=sqs,sns,s3,iam,secretsmanager
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1
      - HOSTNAME_EXTERNAL=localstack
      - DATA_DIR=/var/lib/localstack
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "./localstack-data:/var/lib/localstack"
      - "./scripts/setup-localstack.sh:/etc/localstack/init/ready.d/init-aws.sh"
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - sqs-consumer-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s



  # SQS Consumer Service for Local Development with LocalStack
  sqs-consumer-service:
    container_name: sqs-consumer-service-local
    build:
      context: .
      dockerfile: SQSConsumerService/Dockerfile
    image: sqs-consumer-service-local
    ports:
      - "${HTTP_PORT:-5000}:80"
      - "5001:443"
    environment:
      # Core Application Settings
      - ASPNETCORE_ENVIRONMENT=Local
      - ASPNETCORE_URLS=http://+:80

      # Logging Settings for Local Development
      - Logging__LogLevel__Default=${LOG_LEVEL_DEFAULT:-Debug}
      - Logging__LogLevel__SQSLibrary=${LOG_LEVEL_SQSLIBRARY:-Debug}
      - Logging__LogLevel__SQSConsumerService=${LOG_LEVEL_SQSCONSUMERSERVICE:-Debug}
      - Logging__LogLevel__Microsoft=${LOG_LEVEL_MICROSOFT:-Warning}
      - Logging__LogLevel__System=${LOG_LEVEL_SYSTEM:-Warning}

      # Elasticsearch Settings - Connect to local Elasticsearch instance (optional)
      - Logging__ElasticsearchSettings__Url=http://host.docker.internal:9200
      - Logging__ElasticsearchSettings__UserName=
      - Logging__ElasticsearchSettings__Password=
      - Logging__ElasticsearchSettings__Serilog__EnableSSL=false

      # AWS Settings for LocalStack
      - awsSettings__useLocalStack=true
      - awsSettings__serviceUrl=http://localstack:4566
      - awsSettings__region=${AWS_REGION:-us-east-1}
      - awsSettings__accessKey=test
      - awsSettings__secretKey=test

      # SQS Settings
      - sqsSettings__maxMessages=${SQS_MAX_MESSAGES:-1}
      - sqsSettings__waitTimeSeconds=${SQS_WAIT_TIME_SECONDS:-20}
      - sqsSettings__visibilityTimeoutSeconds=${SQS_VISIBILITY_TIMEOUT_SECONDS:-30}

      # S3 Settings
      - s3Settings__downloadTimeoutSeconds=${S3_DOWNLOAD_TIMEOUT_SECONDS:-30}
      - s3Settings__downloadRetryAttempts=${S3_DOWNLOAD_RETRY_ATTEMPTS:-3}

      # Batch Processing Settings
      - batchProcessingSettings__enableBatchProcessing=${BATCH_PROCESSING_ENABLED:-true}
      - batchProcessingSettings__batchSize=${BATCH_SIZE:-5}
      - batchProcessingSettings__batchTimeoutMs=${BATCH_TIMEOUT_MS:-5000}
      - batchProcessingSettings__enableParallelS3Downloads=${PARALLEL_S3_DOWNLOADS_ENABLED:-true}
      - batchProcessingSettings__maxConcurrentS3Downloads=${MAX_CONCURRENT_S3_DOWNLOADS:-5}

      # Collector API Settings
      - collectorAPISettings__baseUrl=${COLLECTOR_API_BASE_URL:-http://host.docker.internal:62080}
      - collectorAPISettings__eventsEndpoint=${COLLECTOR_API_EVENTS_ENDPOINT:-/api/events/{clientCode}}
      - collectorAPISettings__healthEndpoint=${COLLECTOR_API_HEALTH_ENDPOINT:-/api/health/ping}
      - collectorAPISettings__userId=${COLLECTOR_API_USER_ID:-14c/bpnwiR8rgjvUyzc6QHcnSztStAhgtUMBu0YxulgXqsi+mNgMB/fxPJy0U5wrXri7MBz40CqvJyvBX253/g==}
      - collectorAPISettings__timeoutSeconds=${COLLECTOR_API_TIMEOUT_SECONDS:-30}

      # Health Check Settings
      - healthCheckSettings__enabled=${HEALTH_CHECK_ENABLED:-true}
      - healthCheckSettings__intervalSeconds=${HEALTH_CHECK_INTERVAL_SECONDS:-30}
      - healthCheckSettings__timeoutSeconds=${HEALTH_CHECK_TIMEOUT_SECONDS:-10}
      - healthCheckSettings__maxConsecutiveFailures=${HEALTH_CHECK_MAX_CONSECUTIVE_FAILURES:-3}

      # AWS Secrets Manager (disabled for local development)
      - awsSecretsManager__enabled=${AWS_SECRETS_MANAGER_ENABLED:-false}
      - awsSecretsManager__fallbackToLocal=${AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL:-true}
    volumes:
      - "${LOG_VOLUME_PATH:-./logs}:/app/logs"
    networks:
      - sqs-consumer-network
    depends_on:
      localstack:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/api/health/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped


networks:
  sqs-consumer-network:
    driver: bridge
    name: sqs-consumer-network
