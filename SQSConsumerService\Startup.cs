using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Enrichers.AspnetcoreHttpcontext;
using SQSLibrary;
using SQSLibrary.Configuration;
using SQSConsumerService.Services;
using Microsoft.Extensions.Hosting;

namespace SQSConsumerService
{
    public class Startup
    {
        /// <summary>
        /// Enables a relative log of any SeriLog usage errors - introduced for debugging purposes when required.
        /// </summary>
        protected bool DebugSysLog = false;

        public static System.Collections.Generic.List<SQSManager> ActiveSqsListeners { get; set; }

        public IConfiguration Configuration { get; }


        public Startup(IConfiguration configuration, IServiceProvider provider)
        {
            var logConfiguration = new LoggerConfiguration()
               .Enrich.WithAspnetcoreHttpcontext(provider)
               .ReadFrom.Configuration(configuration);

            // Configure Elasticsearch logging if credentials are available
            var elasticsearchUrl = configuration["Logging:elasticsearchSettings:url"];
            var elasticsearchUserName = configuration["Logging:elasticsearchSettings:userName"];
            var elasticsearchPassword = configuration["Logging:elasticsearchSettings:password"];

            if (!string.IsNullOrWhiteSpace(elasticsearchUrl))
            {
                try
                {
                    // Decrypt the Elasticsearch configuration values
                    string decryptedUrl = SQSLibrary.Encryption.Decrypt(elasticsearchUrl);
                    string decryptedUserName = SQSLibrary.Encryption.Decrypt(elasticsearchUserName);
                    string decryptedPassword = SQSLibrary.Encryption.Decrypt(elasticsearchPassword);

                    if (!Uri.TryCreate(decryptedUrl, UriKind.Absolute, out Uri elasticUri))
                    {
                        Console.WriteLine("Invalid Elasticsearch URL format after decryption. Skipping Elasticsearch logging setup.");
                        return;
                    }

                    string elasticConnectionUrl = string.Empty;
                    if (!string.IsNullOrWhiteSpace(decryptedUserName) && !string.IsNullOrWhiteSpace(decryptedPassword))
                    {
                        elasticConnectionUrl = $"{elasticUri.Scheme}://{decryptedUserName}:{decryptedPassword}@{elasticUri.Host}:{elasticUri.Port}";
                    }
                    else
                    {
                        elasticConnectionUrl = decryptedUrl;
                    }

                    // Configure Elasticsearch sink
                    var elasticsearchOptions = new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(elasticConnectionUrl))
                    {
                        AutoRegisterTemplate = configuration.GetValue<bool>("Logging:elasticsearchSettings:serilog:autoRegisterTemplate"),
                        IndexFormat = configuration["Logging:elasticsearchSettings:serilog:indexFormat"] ?? "application_log_csa_sqs_consumer_service-{0:yyyy.MM.dd}",
                        MinimumLogEventLevel = Enum.TryParse<Serilog.Events.LogEventLevel>(
                            configuration["Logging:elasticsearchSettings:serilog:restrictedToMinimumLevel"],
                            out var logLevel) ? logLevel : Serilog.Events.LogEventLevel.Information,
                        TypeName = null
                    };

                    Console.WriteLine("Elasticsearch connection configured successfully");

                    // Add SSL certificate validation bypass if SSL is not disabled
                    if (configuration.GetValue<bool>("Logging:elasticsearchSettings:serilog:enableSSL", true))
                    {
                        Console.WriteLine("Enabling SSL certificate validation bypass for Elasticsearch");
                        elasticsearchOptions.ModifyConnectionSettings = conn =>
                            conn.ServerCertificateValidationCallback((o, certificate, arg3, arg4) => true);
                    }

                    logConfiguration.WriteTo.Elasticsearch(elasticsearchOptions);
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail startup due to Elasticsearch configuration issues
                    Console.WriteLine("Warning: Failed to configure Elasticsearch logging: {0}", ex.Message);
                }
            }
            // If Elasticsearch configuration is not available, continue without it

            Log.Logger = logConfiguration.CreateLogger();
            Configuration = configuration;

            // Create logger and log startup message
            Code.AboutApiData about = new Code.AboutApiData
            {
                Environment = provider.GetRequiredService<IWebHostEnvironment>().EnvironmentName,
                Name = "SQS Consumer Service",
                Version = Configuration.GetSection("version").Value,
                Build = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
            };

            Log.Logger.Information("SQS Consumer starting up, running {Version}", about.VersionOrBuild);
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_3_0);

            // Configure AWS options based on environment and LocalStack settings
            var awsOptions = Configuration.GetAWSOptions();

            // Read AWS settings from configuration
            var awsSettings = Configuration.GetSection("awsSettings");
            var useLocalStack = awsSettings.GetValue<bool>("useLocalStack");
            var region = awsSettings.GetValue<string>("region");
            var serviceUrl = awsSettings.GetValue<string>("serviceUrl");
            var accessKey = awsSettings.GetValue<string>("accessKey");
            var secretKey = awsSettings.GetValue<string>("secretKey");

            // Set region if specified
            if (!string.IsNullOrEmpty(region))
            {
                awsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(region);
            }

            // Configure for LocalStack if enabled
            if (useLocalStack)
            {
                Log.Logger.Information("Configuring AWS services for LocalStack environment");

                // For LocalStack, we need to configure the service URL and credentials
                if (!string.IsNullOrEmpty(serviceUrl))
                {
                    awsOptions.DefaultClientConfig.ServiceURL = serviceUrl;
                    awsOptions.DefaultClientConfig.UseHttp = true;
                }

                // Set credentials for LocalStack
                if (!string.IsNullOrEmpty(accessKey) && !string.IsNullOrEmpty(secretKey))
                {
                    awsOptions.Credentials = new Amazon.Runtime.BasicAWSCredentials(accessKey, secretKey);
                }

                Log.Logger.Information("LocalStack AWS configuration: ServiceURL={ServiceUrl}, Region={Region}",
                    serviceUrl, region);
            }
            else
            {
                Log.Logger.Information("Configuring AWS services for production environment with region: {Region}", region);

                // For production, use IAM roles or default credential chain
                // Only set explicit credentials if provided (not recommended for production)
                if (!string.IsNullOrEmpty(accessKey) && !string.IsNullOrEmpty(secretKey))
                {
                    Log.Logger.Warning("Using explicit AWS credentials - this should only be used in development");
                    awsOptions.Credentials = new Amazon.Runtime.BasicAWSCredentials(accessKey, secretKey);
                }
            }

            // Register AWS options
            services.AddDefaultAWSOptions(awsOptions);

            // Register configuration objects as singletons
            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var awsSettings = new AwsSettings();
                config.Bind("awsSettings", awsSettings);
                return awsSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var sqsSettings = new SQSSettings();
                config.Bind("sqsSettings", sqsSettings);
                return sqsSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var s3Settings = new S3Settings();
                config.Bind("s3Settings", s3Settings);
                return s3Settings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var batchSettings = new BatchProcessingSettings();
                config.Bind("batchProcessingSettings", batchSettings);
                return batchSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var healthSettings = new HealthCheckSettings();
                config.Bind("healthCheckSettings", healthSettings);
                return healthSettings;
            });

            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var collectorSettings = new CollectorAPISettings();
                config.Bind("collectorAPISettings", collectorSettings);
                return collectorSettings;
            });

            // Register configuration change notification services
            services.AddSingleton<IConfigurationChangeNotificationService, ConfigurationChangeNotificationService>();

            // Register configuration change monitor settings
            services.AddSingleton(provider =>
            {
                var config = provider.GetRequiredService<IConfiguration>();
                var settings = new ConfigurationChangeMonitorSettings();
                config.Bind("configurationChangeMonitor", settings);
                return settings;
            });

            // Register configuration change monitor as hosted service
            services.AddHostedService<ConfigurationChangeMonitor>();

            // Register other custom services
            services.AddSingleton<ISharedHealthCheckerService, SharedHealthCheckerService>();
            services.AddSingleton<IQueueOrchestrator, QueueOrchestrator>();

            Log.Logger.Information("All configuration objects and services registered successfully");
        }

        /// <summary>
        /// Performs the startup of the configured SQS consumers using the queue orchestrator
        /// </summary>
        private static async Task StartConsumersAsync(IServiceProvider serviceProvider)
        {
            try
            {
                var queueOrchestrator = serviceProvider.GetRequiredService<IQueueOrchestrator>();

                // Initialize the orchestrator
                await queueOrchestrator.InitializeAsync(serviceProvider);

                // Start all consumers
                await queueOrchestrator.StartConsumersAsync();

                // Update the static reference for backward compatibility with health controller
                ActiveSqsListeners = new System.Collections.Generic.List<SQSManager>(queueOrchestrator.ActiveConsumers);

                Log.Logger.Information("Queue orchestrator startup completed successfully");
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Failed to start queue orchestrator");
                // Re-throw with additional context
                throw new InvalidOperationException("Queue orchestrator failed to start. See inner exception for details.", ex);
            }
        }



        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public static void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseDefaultFiles();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });

            // Start SQS consumers after the application is configured
            StartConsumersAsync(app.ApplicationServices).GetAwaiter().GetResult();
        }
    }
}
