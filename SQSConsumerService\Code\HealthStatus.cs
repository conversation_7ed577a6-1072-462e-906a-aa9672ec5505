namespace SQSConsumerService.Code
{
    /// <summary>
    /// Enumeration representing the health status of the service
    /// </summary>
    public enum HealthStatus
    {
        /// <summary>
        /// Service is healthy and all components are functioning properly
        /// </summary>
        Healthy,

        /// <summary>
        /// Service is partially functional but some components may be degraded
        /// </summary>
        Degraded,

        /// <summary>
        /// Service is unhealthy and not functioning properly
        /// </summary>
        Unhealthy
    }

    /// <summary>
    /// Extension methods for HealthStatus enum
    /// </summary>
    public static class HealthStatusExtensions
    {
        /// <summary>
        /// Convert HealthStatus enum to lowercase string representation
        /// </summary>
        /// <param name="status">The health status</param>
        /// <returns>Lowercase string representation</returns>
        public static string ToLowerString(this HealthStatus status)
        {
            return status.ToString().ToLower();
        }

        /// <summary>
        /// Parse string to HealthStatus enum
        /// </summary>
        /// <param name="status">String representation of health status</param>
        /// <returns>HealthStatus enum value</returns>
        public static HealthStatus ParseHealthStatus(string status)
        {
            return status?.ToLower() switch
            {
                "healthy" => HealthStatus.Healthy,
                "degraded" => HealthStatus.Degraded,
                "unhealthy" => HealthStatus.Unhealthy,
                _ => HealthStatus.Unhealthy
            };
        }
    }
}
