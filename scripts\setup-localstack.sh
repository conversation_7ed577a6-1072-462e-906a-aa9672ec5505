#!/bin/bash

set -e

echo "-------------------------------------------------------------------"
echo "            LocalStack S3/SNS/SQS Infrastructure Setup            "
echo "-------------------------------------------------------------------"

# Set AWS credentials for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
export AWS_ENDPOINT_URL=http://localhost:4566

echo "Checking LocalStack health..."
if ! curl -f http://localhost:4566/_localstack/health > /dev/null 2>&1; then
    echo "ERROR: LocalStack is not ready. Please check if it's running."
    echo "Try: docker-compose -f docker-compose.local.yml up -d"
    exit 1
fi
echo "LocalStack is ready..."

echo ""
echo "--- Creating S3 Buckets ---"
echo "Creating S3 bucket: local-washingtoncounty-mn-datalake-raw-01"
if aws s3 mb s3://local-washingtoncounty-mn-datalake-raw-01 --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created bucket: local-washingtoncounty-mn-datalake-raw-01"
else
    echo "Failed: local-washingtoncounty-mn-datalake-raw-01"
fi

echo "Creating S3 bucket: local-flaglercounty-mn-datalake-raw-01"
if aws s3 mb s3://local-flaglercounty-mn-datalake-raw-01 --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created bucket: local-flaglercounty-mn-datalake-raw-01"
else
    echo "Failed: local-flaglercounty-mn-datalake-raw-01"
fi

echo ""
echo "--- Creating SNS Topics ---"
echo "Creating SNS topic: local-washingtoncounty-s3-events"
if aws sns create-topic --name local-washingtoncounty-s3-events --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created topic: local-washingtoncounty-s3-events"
else
    echo "Failed: local-washingtoncounty-s3-events"
fi

echo "Creating SNS topic: local-flaglercounty-s3-events"
if aws sns create-topic --name local-flaglercounty-s3-events --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created topic: local-flaglercounty-s3-events"
else
    echo "Failed: local-flaglercounty-s3-events"
fi

echo ""
echo "--- Creating SQS Queues ---"
echo "Creating SQS queue: local-washingtoncounty-logevents-queue"
if aws sqs create-queue --queue-name local-washingtoncounty-logevents-queue --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created queue: local-washingtoncounty-logevents-queue"
else
    echo "Failed: local-washingtoncounty-logevents-queue"
fi

echo "Creating SQS queue: local-flaglercounty-logevents-queue"
if aws sqs create-queue --queue-name local-flaglercounty-logevents-queue --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created queue: local-flaglercounty-logevents-queue"
else
    echo "Failed: local-flaglercounty-logevents-queue"
fi

echo ""
echo "--- Setting up SNS to SQS Subscriptions ---"
echo "Subscribing local-washingtoncounty-logevents-queue to local-washingtoncounty-s3-events"
if aws sns subscribe --topic-arn "arn:aws:sns:us-east-1:000000000000:local-washingtoncounty-s3-events" --protocol sqs --notification-endpoint "arn:aws:sqs:us-east-1:000000000000:local-washingtoncounty-logevents-queue" --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Subscribed local washington county queue to local washington county topic"
else
    echo "Failed to subscribe local washington county queue"
fi

echo "Subscribing local-flaglercounty-logevents-queue to local-flaglercounty-s3-events"
if aws sns subscribe --topic-arn "arn:aws:sns:us-east-1:000000000000:local-flaglercounty-s3-events" --protocol sqs --notification-endpoint "arn:aws:sqs:us-east-1:000000000000:local-flaglercounty-logevents-queue" --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Subscribed local flagler county queue to local flagler county topic"
else
    echo "Failed to subscribe local flagler county queue"
fi

echo ""
echo "--- Setting SQS Queue Policies ---"
echo "Queue policies not required for LocalStack (using default permissive settings)"

echo ""
echo "--- Creating AWS Secrets Manager Secrets ---"
echo "Creating secret: local/us/csa-sqs-consumer-service/configuration"

# Create the secret JSON content with ClientQueues configuration
cat > /tmp/local-secrets-config.json << 'EOF'
{
  "ClientQueues": [
    {
      "clientCode": "brmb",
      "queueName": "local-washingtoncounty-logevents-queue",
      "queueUrl": "http://localhost:4566/000000000000/local-washingtoncounty-logevents-queue",
      "region": "us-east-1"
    },
    {
      "clientCode": "flfl",
      "queueName": "local-flaglercounty-logevents-queue",
      "queueUrl": "http://localhost:4566/000000000000/local-flaglercounty-logevents-queue",
      "region": "us-east-1"
    }
  ]
}
EOF

if aws secretsmanager create-secret \
    --name "local/us/csa-sqs-consumer-service/configuration" \
    --description "Local SQS Consumer Service Configuration for testing" \
    --secret-string file:///tmp/local-secrets-config.json \
    --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Created secret: local/us/csa-sqs-consumer-service/configuration"
else
    echo "Failed to create secret (may already exist)"
fi

# Clean up temporary file
rm -f /tmp/local-secrets-config.json

echo ""
echo "--- Configuring S3 Event Notifications ---"

echo "Creating S3 notification configuration files..."

# Create local washington county bucket notification config
cat > /tmp/local-washingtoncounty-s3-notification.json << 'EOF'
{
  "TopicConfigurations": [
    {
      "Id": "LogEventNotification",
      "TopicArn": "arn:aws:sns:us-east-1:000000000000:local-washingtoncounty-s3-events",
      "Events": ["s3:ObjectCreated:*"],
      "Filter": {
        "Key": {
          "FilterRules": [
            {
              "Name": "suffix",
              "Value": ".xml"
            },
            {
              "Name": "prefix",
              "Value": "CHE/SOLACOM/"
            }
          ]
        }
      }
    }
  ]
}
EOF

# Create local flagler county bucket notification config
cat > /tmp/local-flaglercounty-s3-notification.json << 'EOF'
{
  "TopicConfigurations": [
    {
      "Id": "LogEventNotification",
      "TopicArn": "arn:aws:sns:us-east-1:000000000000:local-flaglercounty-s3-events",
      "Events": ["s3:ObjectCreated:*"],
      "Filter": {
        "Key": {
          "FilterRules": [
            {
              "Name": "suffix",
              "Value": ".xml"
            },
            {
              "Name": "prefix",
              "Value": "CHE/SOLACOM/"
            }
          ]
        }
      }
    }
  ]
}
EOF

echo "Configuring S3 notifications for local washington county bucket"
if aws s3api put-bucket-notification-configuration --bucket local-washingtoncounty-mn-datalake-raw-01 --notification-configuration file:///tmp/local-washingtoncounty-s3-notification.json --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Configured S3 notifications for local washington county bucket"
else
    echo "Failed to configure S3 notifications for local washington county bucket"
fi

echo "Configuring S3 notifications for local flagler county bucket"
if aws s3api put-bucket-notification-configuration --bucket local-flaglercounty-mn-datalake-raw-01 --notification-configuration file:///tmp/local-flaglercounty-s3-notification.json --endpoint-url=$AWS_ENDPOINT_URL; then
    echo "Configured S3 notifications for local flagler county bucket"
else
    echo "Failed to configure S3 notifications for local flagler county bucket"
fi

# Clean up temporary files
rm -f /tmp/local-washingtoncounty-s3-notification.json
rm -f /tmp/local-flaglercounty-s3-notification.json

echo ""
echo "-------------------------------------------------------------------"
echo "    Infrastructure Setup Complete!"
echo "-------------------------------------------------------------------"
echo ""
echo "Created Resources:"
echo "  - S3 Buckets: 2 buckets"
echo "  - SNS Topics: 2 topics"
echo "  - SQS Queues: 2 queues"
echo "  - SNS-SQS Subscriptions: 2 subscriptions"
echo "  - S3 Event Notifications: 2 configurations"
echo "  - AWS Secrets Manager: 1 secret with queue configuration"
echo ""
echo "LocalStack is ready for S3-based LogEvent processing!"