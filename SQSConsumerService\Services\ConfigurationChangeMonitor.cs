using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using SQSLibrary;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Configuration for the configuration change monitor
    /// </summary>
    public class ConfigurationChangeMonitorSettings
    {
        /// <summary>
        /// Whether configuration change monitoring is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Debounce delay in milliseconds to avoid multiple rapid notifications
        /// </summary>
        public int DebounceDelayMs { get; set; } = 5000; // 5 seconds default
    }

    /// <summary>
    /// Monitors configuration changes using IConfiguration change tokens and publishes notifications
    /// This leverages AWS AddSecretsManager built-in polling instead of custom polling
    /// </summary>
    public class ConfigurationChangeMonitor : BackgroundService
    {
        private readonly ILogger<ConfigurationChangeMonitor> _logger;
        private readonly IConfiguration _configuration;
        private readonly ConfigurationChangeMonitorSettings _settings;
        private readonly IConfigurationChangeNotificationService _notificationService;
        private IDisposable _changeTokenRegistration;
        private List<QueueDefinition> _lastKnownQueues;
        private readonly object _lock = new object();
        private DateTime _lastChangeTime = DateTime.MinValue;

        /// <summary>
        /// Initializes a new instance of the ConfigurationChangeMonitor
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="configuration">Configuration instance with AddSecretsManager auto-polling</param>
        /// <param name="settings">Monitor configuration settings</param>
        /// <param name="notificationService">Service for publishing configuration change notifications</param>
        public ConfigurationChangeMonitor(
            ILogger<ConfigurationChangeMonitor> logger,
            IConfiguration configuration,
            ConfigurationChangeMonitorSettings settings,
            IConfigurationChangeNotificationService notificationService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        }

        /// <summary>
        /// Starts monitoring configuration changes
        /// </summary>
        /// <param name="stoppingToken">Cancellation token for stopping the service</param>
        /// <returns>Task representing the background operation</returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            if (!_settings.Enabled)
            {
                _logger.LogInformation("Configuration change monitoring is disabled");
                return;
            }

            _logger.LogInformation("Configuration change monitor starting. Debounce delay: {DebounceDelayMs}ms", _settings.DebounceDelayMs);

            // Load initial configuration
            await LoadInitialConfigurationAsync();

            // Register for configuration change notifications
            RegisterForConfigurationChanges();

            // Keep the service running
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }

        /// <summary>
        /// Loads the initial configuration to establish a baseline
        /// </summary>
        /// <returns>Task representing the load operation</returns>
        private Task LoadInitialConfigurationAsync()
        {
            try
            {
                _lastKnownQueues = LoadQueueDefinitionsFromConfiguration();
                _logger.LogInformation("Loaded initial configuration with {QueueCount} queues", _lastKnownQueues?.Count ?? 0);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load initial configuration");
                _lastKnownQueues = new List<QueueDefinition>();
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Registers for configuration change notifications using IConfiguration change tokens
        /// </summary>
        private void RegisterForConfigurationChanges()
        {
            // Register for changes to the ClientQueues section
            ChangeToken.OnChange(
                () => _configuration.GetReloadToken(),
                async () => await OnConfigurationChangedAsync());

            _logger.LogInformation("Registered for configuration change notifications - AWS Secrets Manager polling is active");
        }

        /// <summary>
        /// Handles configuration change notifications
        /// </summary>
        /// <returns>Task representing the change handling operation</returns>
        /// <exception cref="Exception">Logs errors but does not throw</exception>
        private async Task OnConfigurationChangedAsync()
        {
            try
            {
                lock (_lock)
                {
                    // Debounce rapid changes
                    var now = DateTime.UtcNow;
                    if (now - _lastChangeTime < TimeSpan.FromMilliseconds(_settings.DebounceDelayMs))
                    {
                        _logger.LogDebug("Configuration change debounced");
                        return;
                    }
                    _lastChangeTime = now;
                }

                _logger.LogInformation("Configuration change detected at {Timestamp}, checking for queue definition changes...", DateTime.UtcNow);

                var currentQueues = LoadQueueDefinitionsFromConfiguration();
                var hasChanges = DetectQueueConfigurationChanges(_lastKnownQueues, currentQueues);

                if (hasChanges)
                {
                    _logger.LogInformation("Queue configuration changes detected - {CurrentCount} queues found", currentQueues?.Count ?? 0);
                    _lastKnownQueues = currentQueues;
                    await _notificationService.PublishConfigurationChangedAsync("Queue configuration updated via AWS Secrets Manager polling");
                }
                else
                {
                    _logger.LogDebug("No queue configuration changes detected");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing configuration change");
                // Swallow the exception to avoid stopping the background service
            }
        }

        /// <summary>
        /// Loads queue definitions directly from IConfiguration (updated by Kralizek polling).
        /// This method reads from the "ClientQueues" configuration section and validates each queue definition.
        /// Used for detecting configuration changes when AWS Secrets Manager is updated.
        /// </summary>
        /// <returns>List of validated queue definitions or empty list if none found</returns>
        /// <exception cref="Exception">Logs warnings for invalid queue configurations but does not throw</exception>
        private List<QueueDefinition> LoadQueueDefinitionsFromConfiguration()
        {
            try
            {
                var queues = new List<QueueDefinition>();
                var clientQueuesSection = _configuration.GetSection("ClientQueues");

                if (clientQueuesSection.Value != null || clientQueuesSection.GetChildren().Any())
                {
                    foreach (var queueSection in clientQueuesSection.GetChildren())
                    {
                        var queue = new QueueDefinition
                        {
                            ClientCode = queueSection["ClientCode"],
                            QueueName = queueSection["QueueName"],
                            QueueUrl = queueSection["QueueUrl"],
                            Region = queueSection["Region"]
                        };

                        // Validate required fields
                        if (!string.IsNullOrEmpty(queue.ClientCode) &&
                            !string.IsNullOrEmpty(queue.QueueName) &&
                            !string.IsNullOrEmpty(queue.QueueUrl))
                        {
                            queues.Add(queue);
                        }
                        else
                        {
                            _logger.LogWarning("Skipping invalid queue configuration: ClientCode={ClientCode}, QueueName={QueueName}, QueueUrl={QueueUrl}",
                                queue.ClientCode, queue.QueueName, queue.QueueUrl);
                        }
                    }
                }

                _logger.LogDebug("Loaded {QueueCount} queue definitions from IConfiguration", queues.Count);
                return queues;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load queue definitions from IConfiguration");
                return new List<QueueDefinition>();
            }
        }

        /// <summary>
        /// Detects changes between two sets of queue configurations
        /// </summary>
        /// <param name="previousQueues">Previous queue configurations</param>
        /// <param name="currentQueues">Current queue configurations</param>
        /// <returns>True if changes are detected, false otherwise</returns>
        private static bool DetectQueueConfigurationChanges(List<QueueDefinition> previousQueues, List<QueueDefinition> currentQueues)
        {
            if (previousQueues == null || currentQueues == null)
                return true;

            if (previousQueues.Count != currentQueues.Count)
                return true;

            // Compare each queue configuration
            foreach (var previous in previousQueues)
            {
                var current = currentQueues.FirstOrDefault(q => q.ClientCode == previous.ClientCode);
                if (current == null)
                    return true; // Queue removed

                if (previous.QueueName != current.QueueName ||
                    previous.QueueUrl != current.QueueUrl ||
                    previous.Region != current.Region)
                    return true; // Queue configuration changed
            }

            // Check for new queues
            return currentQueues.Any(current => !previousQueues.Any(q => q.ClientCode == current.ClientCode));
        }

        /// <summary>
        /// Stops the configuration change monitor
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the stop operation</returns>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Configuration change monitor stopping...");
            
            _changeTokenRegistration?.Dispose();
            
            await base.StopAsync(cancellationToken);
        }
    }
}
