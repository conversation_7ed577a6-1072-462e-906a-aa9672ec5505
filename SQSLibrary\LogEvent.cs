using System;
using System.Xml.Linq;
using Serilog;

namespace SQSLibrary
{
    /// <summary>
    /// Represents a parsed LogEvent from XML message body.
    /// </summary>
    public class LogEvent
    {
        private const string SolacomNamespace = "http://solacom.com/Logging";

        public DateTime Timestamp { get; set; }
        public string AgencyOrElement { get; set; }
        public string Agent { get; set; }
        public string CallIdentifier { get; set; }
        public string IncidentIdentifier { get; set; }
        public string EventType { get; set; }
        public string OriginalXml { get; set; }
        public string MessageId { get; set; }
        public string ReceiptHandle { get; set; }

        /// <summary>
        /// Parse LogEvent from XML content downloaded from S3.
        /// Handles both call-based events (with CallIdentifier) and non-call events (e.g., agent events).
        /// </summary>
        /// <param name="xmlContent">XML content of the LogEvent file</param>
        /// <param name="s3ObjectKey">S3 object key for identification</param>
        /// <param name="receiptHandle">SQS Receipt Handle</param>
        /// <returns>Parsed LogEvent or null if parsing fails due to malformed XML</returns>
        /// <exception cref="System.Xml.XmlException">Thrown when XML is malformed and cannot be parsed</exception>
        public static LogEvent ParseFromS3Content(string xmlContent, string s3ObjectKey, string receiptHandle)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlContent))
                {
                    Log.Logger.Warning("Empty XML content for S3 object {S3ObjectKey}", s3ObjectKey);
                    return null;
                }

                var doc = XDocument.Parse(xmlContent);
                var logEventElement = doc.Root;

                if (logEventElement == null || logEventElement.Name.LocalName != "LogEvent")
                {
                    Log.Logger.Warning("Invalid LogEvent XML structure for S3 object {S3ObjectKey}", s3ObjectKey);
                    return null;
                }

                var logEvent = new LogEvent
                {
                    OriginalXml = xmlContent,
                    MessageId = s3ObjectKey, // Use S3 object key as identifier
                    ReceiptHandle = receiptHandle
                };

                // Parse timestamp
                var timestampElement = logEventElement.Element(XName.Get("timestamp", SolacomNamespace));
                if (timestampElement != null && DateTimeOffset.TryParse(timestampElement.Value, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AssumeUniversal, out var dto))
                {
                    logEvent.Timestamp = dto.UtcDateTime;
                }
                else
                {
                    Log.Logger.Warning("Invalid or missing timestamp in LogEvent for S3 object {S3ObjectKey}", s3ObjectKey);
                    logEvent.Timestamp = DateTime.UtcNow; // Fallback to current time, for ordering purposes
                }

                // Parse other fields - missing elements default to empty string (graceful handling)
                logEvent.AgencyOrElement = GetElementValue(logEventElement, "agencyOrElement");
                logEvent.Agent = GetElementValue(logEventElement, "agent");
                logEvent.CallIdentifier = GetElementValue(logEventElement, "callIdentifier");
                logEvent.IncidentIdentifier = GetElementValue(logEventElement, "incidentIdentifier");
                logEvent.EventType = GetElementValue(logEventElement, "eventType");

                Log.Logger.Debug("Successfully parsed LogEvent from S3 object {S3ObjectKey}: {EventType} at {Timestamp}",
                    s3ObjectKey, logEvent.EventType, logEvent.Timestamp);

                return logEvent;
            }
            catch (System.Xml.XmlException ex)
            {
                Log.Logger.Error(ex, "Malformed XML content for S3 object {S3ObjectKey} - cannot process", s3ObjectKey);
                return null; // Malformed XML - cannot process, fail fast
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Unexpected error parsing LogEvent from S3 object {S3ObjectKey}", s3ObjectKey);
                return null;
            }
        }


        /// <summary>
        /// Helper method to get element value with namespace
        /// </summary>
        private static string GetElementValue(XElement parent, string elementName)
        {
            var element = parent.Element(XName.Get(elementName, SolacomNamespace));
            return element?.Value ?? string.Empty;
        }

        /// <summary>
        /// Get sorting key for ordering events.
        /// Primary: Timestamp, Secondary: CallIdentifier (empty CallIdentifier is allowed for non-call events like agent events).
        /// Non-call events are sorted after call events with the same timestamp.
        /// </summary>
        /// <returns>Sorting key string for event ordering</returns>
        public string GetSortingKey()
        {
            // Use "ZZZZZ_NO_CALL_ID" to ensure non-call events sort after call events with same timestamp
            var callId = string.IsNullOrEmpty(CallIdentifier) ? "ZZZZZ_NO_CALL_ID" : CallIdentifier;
            return $"{Timestamp:yyyy-MM-ddTHH:mm:ss.fffZ}|{callId}";
        }

        public override string ToString()
        {
            var callId = string.IsNullOrEmpty(CallIdentifier) ? "NO_CALL_ID" : CallIdentifier;
            return $"LogEvent[{MessageId}] - {Timestamp:yyyy-MM-dd HH:mm:ss.fff} - {EventType} - {callId}";
        }
    }
}
