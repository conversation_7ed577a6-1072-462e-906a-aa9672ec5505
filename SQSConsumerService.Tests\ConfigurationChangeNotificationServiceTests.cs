using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using SQSConsumerService.Services;
using Xunit;

namespace SQSConsumerService.Tests
{
    /// <summary>
    /// Unit tests for ConfigurationChangeNotificationService
    /// </summary>
    public class ConfigurationChangeNotificationServiceTests
    {
        private readonly Mock<ILogger<ConfigurationChangeNotificationService>> _mockLogger;
        private readonly ConfigurationChangeNotificationService _service;

        public ConfigurationChangeNotificationServiceTests()
        {
            _mockLogger = new Mock<ILogger<ConfigurationChangeNotificationService>>();
            _service = new ConfigurationChangeNotificationService(_mockLogger.Object);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ConfigurationChangeNotificationService(null));
        }

        [Fact]
        public void Subscribe_WithNullHandler_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _service.Subscribe(null));
        }

        [Fact]
        public void Unsubscribe_WithNullHandler_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _service.Unsubscribe(null));
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithNoSubscribers_ShouldCompleteSuccessfully()
        {
            // Act
            await _service.PublishConfigurationChangedAsync("Test change");

            // Assert - Should complete without throwing
            Assert.True(true);
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithSubscriber_ShouldNotifySubscriber()
        {
            // Arrange
            var eventReceived = false;
            ConfigurationChangedEventArgs receivedArgs = null;

            void Handler(object sender, ConfigurationChangedEventArgs e)
            {
                eventReceived = true;
                receivedArgs = e;
            }

            _service.Subscribe(Handler);

            // Act
            await _service.PublishConfigurationChangedAsync("Test configuration change");

            // Assert
            Assert.True(eventReceived);
            Assert.NotNull(receivedArgs);
            Assert.Equal("Test configuration change", receivedArgs.Details);
            Assert.Equal("QueueConfiguration", receivedArgs.ConfigurationType);
            Assert.True(receivedArgs.Timestamp <= DateTime.UtcNow);
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithMultipleSubscribers_ShouldNotifyAllSubscribers()
        {
            // Arrange
            var subscriber1Called = false;
            var subscriber2Called = false;

            void Handler1(object sender, ConfigurationChangedEventArgs e) => subscriber1Called = true;
            void Handler2(object sender, ConfigurationChangedEventArgs e) => subscriber2Called = true;

            _service.Subscribe(Handler1);
            _service.Subscribe(Handler2);

            // Act
            await _service.PublishConfigurationChangedAsync("Test change");

            // Assert
            Assert.True(subscriber1Called);
            Assert.True(subscriber2Called);
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithUnsubscribedHandler_ShouldNotNotifyUnsubscribedHandler()
        {
            // Arrange
            var subscriberCalled = false;
            var unsubscribedCalled = false;

            void SubscribedHandler(object sender, ConfigurationChangedEventArgs e) => subscriberCalled = true;
            void UnsubscribedHandler(object sender, ConfigurationChangedEventArgs e) => unsubscribedCalled = true;

            _service.Subscribe(SubscribedHandler);
            _service.Subscribe(UnsubscribedHandler);
            _service.Unsubscribe(UnsubscribedHandler);

            // Act
            await _service.PublishConfigurationChangedAsync("Test change");

            // Assert
            Assert.True(subscriberCalled);
            Assert.False(unsubscribedCalled);
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithExceptionInHandler_ShouldContinueWithOtherHandlers()
        {
            // Arrange
            var goodHandlerCalled = false;

            void ThrowingHandler(object sender, ConfigurationChangedEventArgs e) => throw new InvalidOperationException("Test exception");
            void GoodHandler(object sender, ConfigurationChangedEventArgs e) => goodHandlerCalled = true;

            _service.Subscribe(ThrowingHandler);
            _service.Subscribe(GoodHandler);

            // Act
            await _service.PublishConfigurationChangedAsync("Test change");

            // Assert
            Assert.True(goodHandlerCalled);
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithNullDetails_ShouldUseDefaultMessage()
        {
            // Arrange
            ConfigurationChangedEventArgs receivedArgs = null;

            void Handler(object sender, ConfigurationChangedEventArgs e) => receivedArgs = e;

            _service.Subscribe(Handler);

            // Act
            await _service.PublishConfigurationChangedAsync(null);

            // Assert
            Assert.NotNull(receivedArgs);
            Assert.Equal("Queue configuration changed", receivedArgs.Details);
        }

        [Fact]
        public async Task PublishConfigurationChangedAsync_WithEmptyDetails_ShouldUseDefaultMessage()
        {
            // Arrange
            ConfigurationChangedEventArgs receivedArgs = null;

            void Handler(object sender, ConfigurationChangedEventArgs e) => receivedArgs = e;

            _service.Subscribe(Handler);

            // Act
            await _service.PublishConfigurationChangedAsync("");

            // Assert
            Assert.NotNull(receivedArgs);
            Assert.Equal("Queue configuration changed", receivedArgs.Details);
        }
    }
}
