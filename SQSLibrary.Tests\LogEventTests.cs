using System;
using Xunit;
using FluentAssertions;
using SQSLibrary;

namespace SQSLibrary.Tests
{
    /// <summary>
    /// Unit tests for LogEvent parsing and functionality
    /// </summary>
    public class LogEventTests
    {
        private const string ValidLogEventXml = @"<?xml version=""1.0"" encoding=""utf-8""?>
            <LogEvent xmlns=""http://solacom.com/Logging"">
                <timestamp>2025-01-15T10:30:45.123Z</timestamp>
                <agencyOrElement>TestAgency</agencyOrElement>
                <agent>Agent001</agent>
                <callIdentifier>CALL_001</callIdentifier>
                <incidentIdentifier>INCIDENT_001</incidentIdentifier>
                <eventType>StartCall</eventType>
                <startCall>
                    <header><![CDATA[Test SIP Header]]></header>
                    <location>Test Location</location>
                    <mediaLabel>ML_001</mediaLabel>
                    <callType>Emergency</callType>
                    <signallingType>VOIP</signallingType>
                    <callerName><PERSON></callerName>
                </startCall>
            </LogEvent>";

        [Fact]
        public void ParseFromS3Content_ValidXml_ShouldParseCorrectly()
        {
            // Arrange
            var messageId = "test-message-001";
            var receiptHandle = "test-receipt-handle";

            // Act
            var logEvent = LogEvent.ParseFromS3Content(ValidLogEventXml, messageId, receiptHandle);

            // Assert
            logEvent.Should().NotBeNull();
            logEvent.Timestamp.Should().Be(new DateTime(2025, 1, 15, 10, 30, 45, 123, DateTimeKind.Utc));
            logEvent.AgencyOrElement.Should().Be("TestAgency");
            logEvent.Agent.Should().Be("Agent001");
            logEvent.CallIdentifier.Should().Be("CALL_001");
            logEvent.IncidentIdentifier.Should().Be("INCIDENT_001");
            logEvent.EventType.Should().Be("StartCall");
            logEvent.MessageId.Should().Be(messageId);
            logEvent.ReceiptHandle.Should().Be(receiptHandle);
            logEvent.OriginalXml.Should().Be(ValidLogEventXml);
        }

        [Fact]
        public void ParseFromS3Content_InvalidXml_ShouldReturnNull()
        {
            // Arrange
            var invalidXml = "This is not valid XML";
            var messageId = "test-message-002";
            var receiptHandle = "test-receipt-handle-2";

            // Act
            var logEvent = LogEvent.ParseFromS3Content(invalidXml, messageId, receiptHandle);

            // Assert
            logEvent.Should().BeNull();
        }

        [Fact]
        public void ParseFromS3Content_EmptyXml_ShouldReturnNull()
        {
            // Arrange
            var emptyXml = "";
            var messageId = "test-message-003";
            var receiptHandle = "test-receipt-handle-3";

            // Act
            var logEvent = LogEvent.ParseFromS3Content(emptyXml, messageId, receiptHandle);

            // Assert
            logEvent.Should().BeNull();
        }

        [Fact]
        public void ParseFromS3Content_NullXml_ShouldReturnNull()
        {
            // Arrange
            string nullXml = null;
            var messageId = "test-message-004";
            var receiptHandle = "test-receipt-handle-4";

            // Act
            var logEvent = LogEvent.ParseFromS3Content(nullXml, messageId, receiptHandle);

            // Assert
            logEvent.Should().BeNull();
        }

        [Theory]
        [InlineData("StartCall")]
        [InlineData("Media")]
        [InlineData("EndMedia")]
        [InlineData("EndCall")]
        public void ParseFromS3Content_DifferentEventTypes_ShouldParseCorrectly(string eventType)
        {
            // Arrange
            var xmlWithEventType = ValidLogEventXml.Replace("StartCall", eventType);
            var messageId = $"test-message-{eventType}";
            var receiptHandle = $"test-receipt-handle-{eventType}";

            // Act
            var logEvent = LogEvent.ParseFromS3Content(xmlWithEventType, messageId, receiptHandle);

            // Assert
            logEvent.Should().NotBeNull();
            logEvent.EventType.Should().Be(eventType);
        }

        [Fact]
        public void GetSortingKey_ShouldReturnCorrectFormat()
        {
            // Arrange
            var logEvent = LogEvent.ParseFromS3Content(ValidLogEventXml, "test-msg", "test-receipt");

            // Act
            var sortingKey = logEvent.GetSortingKey();

            // Assert
            // The sorting key should contain the timestamp, call identifier, and event type
            sortingKey.Should().Contain("CALL_001");
            sortingKey.Should().Contain("2025-01-15T");
            sortingKey.Should().MatchRegex(@"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\|CALL_001$");
        }

        [Fact]
        public void ToString_ShouldReturnCorrectFormat()
        {
            // Arrange
            var logEvent = LogEvent.ParseFromS3Content(ValidLogEventXml, "test-msg-123", "test-receipt");

            // Act
            var stringRepresentation = logEvent.ToString();

            // Assert
            // The string should contain the message ID, event type, and call identifier
            stringRepresentation.Should().Contain("test-msg-123");
            stringRepresentation.Should().Contain("StartCall");
            stringRepresentation.Should().Contain("CALL_001");
            stringRepresentation.Should().Contain("2025-01-15");
            stringRepresentation.Should().MatchRegex(@"LogEvent\[test-msg-123\] - \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3} - StartCall - CALL_001");
        }

        [Fact]
        public void ParseFromS3Content_MissingNamespace_ShouldHandleGracefully()
        {
            // Arrange
            var xmlWithoutNamespace = @"<?xml version=""1.0"" encoding=""utf-8""?>
                                        <LogEvent>
                                            <timestamp>2025-01-15T10:30:45.123Z</timestamp>
                                            <agencyOrElement>TestAgency</agencyOrElement>
                                            <agent>Agent001</agent>
                                            <callIdentifier>CALL_001</callIdentifier>
                                            <incidentIdentifier>INCIDENT_001</incidentIdentifier>
                                            <eventType>StartCall</eventType>
                                        </LogEvent>";

            // Act
            var logEvent = LogEvent.ParseFromS3Content(xmlWithoutNamespace, "test-msg", "test-receipt");

            // Assert
            // The parser will create a LogEvent but with empty values since namespace is required
            logEvent.Should().NotBeNull();
            logEvent.AgencyOrElement.Should().BeEmpty(); // No namespace means empty values
            logEvent.CallIdentifier.Should().BeEmpty();
            logEvent.EventType.Should().BeEmpty();
        }
    }
}
