version: '3.8'

services:
  localstack:
    container_name: localstack-s3-sqs
    image: localstack/localstack:3.0
    ports:
      - "127.0.0.1:4566:4566"
      - "127.0.0.1:4510-4559:4510-4559"
    environment:
      - DEBUG=1
      - PERSISTENCE=1
      - SKIP_INFRA_DOWNLOADS=1
      - SERVICES=sqs,sns,s3,iam,secretsmanager
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1
      - HOSTNAME_EXTERNAL=localstack
      - DATA_DIR=/var/lib/localstack
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "./localstack-data:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./scripts/setup-localstack.sh:/etc/localstack/init/ready.d/init-aws.sh"
    networks:
      - localstack-network

networks:
  localstack-network:
    driver: bridge
