# SQS Consumer Service Environment Configuration
# Copy this file to .env and modify values as needed for your environment
#
# This file shows all configurable environment variables that can override
# appsettings.json values when running the service in Docker containers.


# ENVIRONMENT SELECTION
# Environment name - determines which appsettings.{Environment}.json file to load
# Available options:
#   - Local: For local development with LocalStack
#   - Development: For dev environment with AWS services
#   - QA: For QA environment
#   - Production: For production US environment
#   - Production-ca: For production Canada environment
#   - Production-ca-fr: For production Canada French environment
ASPNETCORE_ENVIRONMENT=Local

# DOCKER CONFIGURATION
# Docker image tag for builds
IMAGE_TAG=latest

# HTTP port mapping for the service (host:container)
HTTP_PORT=8080

# Resource limits for Docker deployment
MEMORY_LIMIT=512M
CPU_LIMIT=1.0
MEMORY_RESERVATION=256M
CPU_RESERVATION=0.5

# AWS CONFIGURATION
# AWS Region (us-east-1, ca-central-1, etc.)
AWS_REGION=us-east-1

# SQS CONFIGURATION
# Maximum number of messages to retrieve in a single SQS request (1-10)
SQS_MAX_MESSAGES=1

# Long polling wait time in seconds (0-20)
SQS_WAIT_TIME_SECONDS=20

# Message visibility timeout in seconds (0-43200)
SQS_VISIBILITY_TIMEOUT_SECONDS=30

# S3 CONFIGURATION
# S3 download timeout in seconds
S3_DOWNLOAD_TIMEOUT_SECONDS=30

# Number of retry attempts for failed S3 downloads
S3_DOWNLOAD_RETRY_ATTEMPTS=3

# BATCH PROCESSING CONFIGURATION
# Enable/disable batch processing
BATCH_PROCESSING_ENABLED=true

# Number of events to batch before sending to collector API
# Local/Dev: 5, QA: 5, Production: 1
BATCH_SIZE=5

# Maximum time to wait for batch completion in milliseconds
BATCH_TIMEOUT_MS=5000

# Enable/disable parallel S3 downloads for better performance
PARALLEL_S3_DOWNLOADS_ENABLED=true

# Maximum number of concurrent S3 downloads
MAX_CONCURRENT_S3_DOWNLOADS=5

# COLLECTOR API CONFIGURATION
# Base URL for the collector API
# Local: http://localhost:62080
# Dev/QA/Prod: Use actual collector API URL
COLLECTOR_API_BASE_URL=http://localhost:62080

# API endpoint for sending events (supports {clientCode} placeholder)
COLLECTOR_API_EVENTS_ENDPOINT=/api/events/{clientCode}

# Health check endpoint for the collector API
COLLECTOR_API_HEALTH_ENDPOINT=/api/health/ping

# Encrypted user ID for collector API authentication
# Use actual encrypted values in production environments
COLLECTOR_API_USER_ID=your-encrypted-user-id-here

# API timeout in seconds
COLLECTOR_API_TIMEOUT_SECONDS=30

# HEALTH CHECK CONFIGURATION
# Enable/disable health checking
HEALTH_CHECK_ENABLED=true

# Health check interval in seconds
# Local/QA: 30, Production: 60
HEALTH_CHECK_INTERVAL_SECONDS=30

# Health check timeout in seconds
# Local/QA: 10, Production: 15
HEALTH_CHECK_TIMEOUT_SECONDS=10

# Maximum consecutive failures before marking unhealthy
# Local/QA: 3, Production: 5
HEALTH_CHECK_MAX_CONSECUTIVE_FAILURES=3

# AWS SECRETS MANAGER CONFIGURATION
# Enable/disable AWS Secrets Manager for configuration
AWS_SECRETS_MANAGER_ENABLED=true

# Secret name in AWS Secrets Manager
# Local: (empty - uses local config)
# Dev: dev/us/csa-sqs-consumer-service/configuration
# QA: qa/us/csa-sqs-consumer-service/configuration
# Production: prod/us/csa-sqs-consumer-service/configuration
AWS_SECRETS_MANAGER_SECRET_NAME=

# AWS region for Secrets Manager
AWS_SECRETS_MANAGER_REGION=us-east-1

# Fallback to local configuration if Secrets Manager fails
# Local/Dev: true, QA/Production: false
AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL=true

# LOGGING CONFIGURATION
# Default log level (Debug, Information, Warning, Error)
# Local/Dev: Debug, QA: Information, Production: Warning
LOG_LEVEL_DEFAULT=Debug

# SQSLibrary log level
# Local/Dev: Debug, QA/Production: Information
LOG_LEVEL_SQSLIBRARY=Debug

# SQSConsumerService log level
# Local/Dev: Debug, QA/Production: Information
LOG_LEVEL_SQSCONSUMERSERVICE=Debug

# Microsoft framework log level
LOG_LEVEL_MICROSOFT=Warning

# System log level
# Local/Dev/QA: Warning, Production: Error
LOG_LEVEL_SYSTEM=Warning

# VOLUME CONFIGURATION
# Path for log files (host path that maps to /app/logs in container)
LOG_VOLUME_PATH=./logs

# ENVIRONMENT-SPECIFIC EXAMPLES
# Example configurations for different environments:

# LOCAL DEVELOPMENT (with LocalStack):
# ASPNETCORE_ENVIRONMENT=Local
# BATCH_SIZE=5
# LOG_LEVEL_DEFAULT=Debug
# AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL=true
# COLLECTOR_API_BASE_URL=http://localhost:62080

# DEVELOPMENT ENVIRONMENT:
# ASPNETCORE_ENVIRONMENT=Development
# BATCH_SIZE=5
# LOG_LEVEL_DEFAULT=Debug
# AWS_SECRETS_MANAGER_SECRET_NAME=dev/us/csa-sqs-consumer-service/configuration
# AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL=true

# QA ENVIRONMENT:
# ASPNETCORE_ENVIRONMENT=QA
# BATCH_SIZE=5
# LOG_LEVEL_DEFAULT=Information
# AWS_SECRETS_MANAGER_SECRET_NAME=qa/us/csa-sqs-consumer-service/configuration
# AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL=false
# HEALTH_CHECK_INTERVAL_SECONDS=30

# PRODUCTION ENVIRONMENT:
# ASPNETCORE_ENVIRONMENT=Production
# BATCH_SIZE=1
# LOG_LEVEL_DEFAULT=Warning
# LOG_LEVEL_SYSTEM=Error
# AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL=false
# HEALTH_CHECK_INTERVAL_SECONDS=60
# HEALTH_CHECK_TIMEOUT_SECONDS=15
# HEALTH_CHECK_MAX_CONSECUTIVE_FAILURES=5

# PRODUCTION CANADA ENVIRONMENT:
# ASPNETCORE_ENVIRONMENT=Production-ca
# AWS_REGION=ca-central-1
# AWS_SECRETS_MANAGER_REGION=ca-central-1
# BATCH_SIZE=1
# LOG_LEVEL_DEFAULT=Warning
# AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL=false