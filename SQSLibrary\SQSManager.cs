using Amazon.SQS;
using Amazon.SQS.Model;
using Amazon;
using Serilog;
using System;
using System.Threading.Tasks;
using System.Threading;
using SQSLibrary.Services;
using SQSLibrary.Configuration;
using SQSLibrary.Logging;

namespace SQSLibrary
{
    /// <summary>
    /// Support function to enable key SQS functionality to be available.
    /// </summary>
    public partial class SQSManager : IDisposable
    {
        private static readonly ILogger _logger = LoggerFactory.ForContext<SQSManager>();
        public IAmazonSQS SqsClient { get; set; }

        /// <summary>
        /// Defines the Queue
        /// </summary>
        public QueueDefinition QueueDefinition { get; set; }

        private AwsSettings _awsSettings { get; set; }
        private SQSSettings _sqsSettings { get; set; }
        private S3Settings _s3Settings { get; set; }
        private BatchProcessingSettings _batchSettings { get; set; }
        private HealthCheckSettings _healthSettings { get; set; }
        private CollectorAPISettings _collectorAPISettings { get; set; }

        private CancellationTokenSource _cancellationTokenSource;
        private Task _pollingTask;
        private BatchProcessor _batchProcessor;
        private S3FileDownloader _s3FileDownloader;
        private IHealthChecker _healthChecker;
        private bool _disposed = false;
        private bool _isHealthy = false;

        /// <summary>
        /// Gets the health status of this SQS consumer
        /// </summary>
        public bool IsHealthy => _isHealthy;

        public SQSManager(
            AwsSettings awsSettings,
            SQSSettings sqsSettings,
            S3Settings s3Settings,
            BatchProcessingSettings batchSettings,
            HealthCheckSettings healthSettings,
            CollectorAPISettings collectorAPISettings,
            QueueDefinition queueDefinition,
            IHealthChecker healthChecker = null)
        {
            QueueDefinition = queueDefinition;
            _awsSettings = awsSettings ?? throw new ArgumentNullException(nameof(awsSettings));
            _sqsSettings = sqsSettings ?? throw new ArgumentNullException(nameof(sqsSettings));
            _s3Settings = s3Settings ?? throw new ArgumentNullException(nameof(s3Settings));
            _batchSettings = batchSettings ?? throw new ArgumentNullException(nameof(batchSettings));
            _healthSettings = healthSettings ?? throw new ArgumentNullException(nameof(healthSettings));
            _collectorAPISettings = collectorAPISettings ?? throw new ArgumentNullException(nameof(collectorAPISettings));
            _cancellationTokenSource = new CancellationTokenSource();
            _healthChecker = healthChecker; // Use injected health checker

            // Initialize S3 file downloader
            _s3FileDownloader = new S3FileDownloader(_awsSettings, _s3Settings, _batchSettings);

            // Initialize batch processor if batch processing is enabled
            if (_batchSettings.EnableBatchProcessing)
            {
                _batchProcessor = new BatchProcessor(_batchSettings.BatchSize, _batchSettings.BatchTimeoutMs);
            }
        }

        /// <summary>
        /// Creates the SQS client connection
        /// </summary>
        private void CreateSQSClient()
        {
            try
            {
                var config = new AmazonSQSConfig
                {
                    RegionEndpoint = RegionEndpoint.GetBySystemName(_awsSettings.Region)
                };

                // Configure for LocalStack if enabled
                if (_awsSettings.UseLocalStack && !string.IsNullOrEmpty(_awsSettings.ServiceUrl))
                {
                    config.ServiceURL = _awsSettings.ServiceUrl;
                    config.UseHttp = true;
                    SqsClient = new AmazonSQSClient(_awsSettings.AccessKey, _awsSettings.SecretKey, config);
                }
                else if (!string.IsNullOrEmpty(_awsSettings.AccessKey) && !string.IsNullOrEmpty(_awsSettings.SecretKey))
                {
                    // Use explicit credentials
                    SqsClient = new AmazonSQSClient(_awsSettings.AccessKey, _awsSettings.SecretKey, config);
                }
                else
                {
                    // Use default credential chain (IAM roles, etc.)
                    SqsClient = new AmazonSQSClient(config);
                }

                _logger.Information("SQS Client created successfully for region: {Region}", _awsSettings.Region);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to create SQS client");
                throw;
            }
        }

        /// <summary>
        /// Creates and checks connection to a defined Queue 
        /// </summary>
        public async Task GenerateConnectionToQueueAsync()
        {
            _logger.Information("Checking SQS Queue status {QueueDefinition}", QueueDefinition.ToString());

            if (SqsClient == null)
            {
                CreateSQSClient();
            }

            try
            {
                // If QueueUrl is not provided, try to get it by queue name
                if (string.IsNullOrEmpty(QueueDefinition.QueueUrl))
                {
                    var getQueueUrlRequest = new GetQueueUrlRequest
                    {
                        QueueName = QueueDefinition.QueueName
                    };

                    var response = await SqsClient.GetQueueUrlAsync(getQueueUrlRequest);
                    QueueDefinition.QueueUrl = response.QueueUrl;
                }

                // Verify queue exists by getting attributes
                var getAttributesRequest = new GetQueueAttributesRequest
                {
                    QueueUrl = QueueDefinition.QueueUrl,
                    AttributeNames = new System.Collections.Generic.List<string> { "QueueArn" }
                };

                await SqsClient.GetQueueAttributesAsync(getAttributesRequest);
                _logger.Information("SQS Queue configuration verified for {QueueName}:{QueueUrl}::{ClientCode}",
                    QueueDefinition.QueueName, QueueDefinition.QueueUrl, QueueDefinition.ClientCode);
                _isHealthy = true;
            }
            catch (QueueDoesNotExistException ex)
            {
                _logger.Error(ex, "SQS Queue does not exist: {QueueName}. This consumer will be marked as unhealthy.", QueueDefinition.QueueName);
                _isHealthy = false;
                throw new InvalidOperationException($"Queue '{QueueDefinition.QueueName}' does not exist", ex);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to verify SQS queue: {QueueName}. This consumer will be marked as unhealthy.", QueueDefinition.QueueName);
                _isHealthy = false;
                throw;
            }
        }

        /// <summary>
        /// Publish a message to target SQS Queue
        /// </summary>
        /// <param name="message"></param>
        public async Task PublishMessageAsync(string message)
        {
            if (SqsClient == null)
            {
                CreateSQSClient();
            }

            try
            {
                var sendMessageRequest = new SendMessageRequest
                {
                    QueueUrl = QueueDefinition.QueueUrl,
                    MessageBody = message
                };

                var response = await SqsClient.SendMessageAsync(sendMessageRequest);
                _logger.Verbose("Sent message to SQS Queue: {QueueDefinition}, MessageId: {MessageId}, message: {Message}", QueueDefinition.ToString(), response.MessageId, message);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to send message to SQS queue {QueueDefinition}", QueueDefinition.ToString());
                throw;
            }
        }

        /// <summary>
        /// Synchronous version of PublishMessageAsync for backward compatibility
        /// </summary>
        /// <param name="message"></param>
        public void PublishMessage(string message)
        {
            PublishMessageAsync(message).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            Dispose(true);
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _cancellationTokenSource?.Cancel();
                _pollingTask?.Wait(TimeSpan.FromSeconds(5));
                SqsClient?.Dispose();
                _s3FileDownloader?.Dispose();
                // Don't dispose the health checker as it's shared across all consumers
                _cancellationTokenSource?.Dispose();
                _disposed = true;
            }
        }
    }
}


