using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using SQSLibrary;
using SQSLibrary.Services;
using SQSLibrary.Models;
using Microsoft.Extensions.Configuration;
using System.IO;
using Newtonsoft.Json;
using SQSLibrary.Configuration;
using System.Net.Http;
using System.Net;

namespace SQSConsumerService.IntegrationTests
{
    /// <summary>
    /// Integration tests that require LocalStack to be running
    /// These tests verify the complete S3->SQS->Consumer flow
    /// </summary>
    [Collection("LocalStack")]
    public class LocalStackIntegrationTests : IDisposable
    {
        private readonly SQSSettings _sqsSettings;
        private readonly AwsSettings _awsSettings;
        private readonly S3Settings _s3Settings;
        private readonly QueueDefinition _queueDefinition;
        private readonly BatchProcessingSettings _batchSettings;
        private readonly HealthCheckSettings _healthSettings;
        private readonly CollectorAPISettings _collectorAPISettings;
        private readonly S3FileDownloader _s3FileDownloader;
        private bool _disposed = false;

        public LocalStackIntegrationTests()
        {
            // Load test configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.Test.json", optional: false)
                .Build();

            _sqsSettings = new SQSSettings();
            configuration.Bind("sqsSettings", _sqsSettings);

            _awsSettings = new AwsSettings();
            configuration.Bind("awsSettings", _awsSettings);

            _batchSettings = new BatchProcessingSettings();
            configuration.Bind("batchProcessingSettings", _batchSettings);

            _s3Settings = new S3Settings();
            configuration.Bind("s3Settings", _s3Settings);

            _healthSettings = new HealthCheckSettings();
            configuration.Bind("healthCheckSettings", _healthSettings);

            _collectorAPISettings = new CollectorAPISettings();
            configuration.Bind("collectorAPISettings", _collectorAPISettings);

            var clientQueues = new List<QueueDefinition>();
            configuration.Bind("ClientQueues", clientQueues);
            _queueDefinition = clientQueues[0];

            _s3FileDownloader = new S3FileDownloader(_awsSettings, _s3Settings, _batchSettings);
        }

        [Fact]
        [Trait("Category", "Integration")]
        [Trait("Requires", "LocalStack")]
        public async Task S3FileDownloader_DownloadLogEventsAsync_ShouldHandleNonExistentObjects()
        {
            // Arrange
            var s3Objects = new List<S3ObjectInfo>
            {
                new S3ObjectInfo
                {
                    BucketName = "non-existent-bucket",
                    ObjectKey = "non-existent-file.xml",
                    Size = 1024,
                    EventTime = DateTime.UtcNow,
                    EventName = "ObjectCreated:Put",
                    Region = "us-east-1",
                    SqsReceiptHandle = "test-receipt-handle",
                    SqsMessageId = "test-message-id"
                }
            };

            // Act
            var results = await _s3FileDownloader.DownloadLogEventsAsync(s3Objects);

            // Assert
            results.Should().HaveCount(1);
            results[0].IsSuccess.Should().BeFalse();
            results[0].ErrorMessage.Should().NotBeNullOrEmpty();
        }

        [Fact]
        [Trait("Category", "Integration")]
        [Trait("Requires", "LocalStack")]
        public void SQSManager_Constructor_ShouldInitializeWithLocalStackSettings()
        {
            // Act
            var sqsManager = new SQSManager(_awsSettings, _sqsSettings, _s3Settings, _batchSettings, _healthSettings, _collectorAPISettings, _queueDefinition);

            // Assert
            sqsManager.Should().NotBeNull();
            sqsManager.QueueDefinition.Should().Be(_queueDefinition);
        }

        [Fact]
        [Trait("Category", "Integration")]
        public void BatchProcessor_EndToEndFlow_ShouldProcessEventsCorrectly()
        {
            // Arrange
            var batchProcessor = new BatchProcessor(_batchSettings.BatchSize, _batchSettings.BatchTimeoutMs);
            var downloadResults = CreateTestDownloadResults(5);

            // Act
            var parsedEvents = batchProcessor.AddS3DownloadResults(downloadResults);
            var orderedEvents = batchProcessor.GetOrderedBatch();

            // Assert
            parsedEvents.Should().HaveCount(5);
            orderedEvents.Should().HaveCount(5);

            // Verify ordering
            for (int i = 1; i < orderedEvents.Count; i++)
            {
                orderedEvents[i].Timestamp.Should().BeOnOrAfter(orderedEvents[i - 1].Timestamp);
            }
        }

        [Fact]
        [Trait("Category", "Integration")]
        public void S3EventParser_ParseComplexS3Event_ShouldHandleMultipleRecords()
        {
            // Arrange
            var complexS3Event = CreateComplexS3EventJson();

            // Act
            var parsedEvent = S3EventParser.ParseS3EventNotification(complexS3Event, "test-complex-message");
            var s3Objects = S3EventParser.ExtractS3Objects(parsedEvent, "test-receipt", "test-message");

            // Assert
            parsedEvent.Should().NotBeNull();
            parsedEvent.Records.Should().HaveCount(3);
            s3Objects.Should().HaveCount(3);

            s3Objects.Should().OnlyContain(obj => obj.BucketName == "test-integration-bucket");
            s3Objects.Should().OnlyContain(obj => obj.Region == "us-east-1");
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task S3FileDownloader_ParallelDownloads_ShouldRespectConcurrencyLimits()
        {
            // Arrange
            var s3Objects = new List<S3ObjectInfo>();
            for (int i = 0; i < 10; i++)
            {
                s3Objects.Add(new S3ObjectInfo
                {
                    BucketName = "test-bucket",
                    ObjectKey = $"test-file-{i}.xml",
                    Size = 1024,
                    EventTime = DateTime.UtcNow,
                    EventName = "ObjectCreated:Put",
                    Region = "us-east-1"
                });
            }

            // Act
            var startTime = DateTime.UtcNow;
            var results = await _s3FileDownloader.DownloadLogEventsAsync(s3Objects);
            var elapsed = DateTime.UtcNow - startTime;

            // Assert
            results.Should().HaveCount(10);
            // All should fail since objects don't exist, but should complete quickly due to parallel processing
            elapsed.TotalSeconds.Should().BeLessThan(30);
        }

        [Fact]
        [Trait("Category", "Integration")]
        [Trait("Requires", "LocalStack")]
        public async Task LocalStack_SQSConnection_ShouldBeAccessible()
        {
            // Arrange
            var sqsManager = new SQSManager(_awsSettings, _sqsSettings, _s3Settings, _batchSettings, _healthSettings, _collectorAPISettings, _queueDefinition);

            // Act & Assert
            try
            {
                await sqsManager.GenerateConnectionToQueueAsync();
                // If we get here without exception, LocalStack SQS is accessible
                sqsManager.SqsClient.Should().NotBeNull();
            }
            catch (Exception ex)
            {
                // Provide helpful error message for LocalStack setup
                throw new InvalidOperationException(
                    $"LocalStack SQS connection failed. Ensure LocalStack is running on {_awsSettings.ServiceUrl}. " +
                    $"Original error: {ex.Message}", ex);
            }
        }

        [Fact]
        [Trait("Category", "Integration")]
        [Trait("Requires", "LocalStack")]
        public async Task LocalStack_HealthCheck_ShouldVerifyServicesAvailable()
        {
            // Arrange
            var localStackHealthUrl = $"{_awsSettings.ServiceUrl}/_localstack/health";

            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);

            // Act
            try
            {
                var response = await httpClient.GetAsync(localStackHealthUrl);
                var content = await response.Content.ReadAsStringAsync();

                // Assert
                response.StatusCode.Should().Be(HttpStatusCode.OK);
                content.Should().Contain("\"sqs\":");
                content.Should().Contain("\"s3\":");
                content.Should().Contain("\"sns\":");
            }
            catch (HttpRequestException ex)
            {
                throw new InvalidOperationException(
                    $"LocalStack health check failed. Ensure LocalStack is running on {_awsSettings.ServiceUrl}. " +
                    $"Run 'start-localstack.bat' to start LocalStack. Original error: {ex.Message}", ex);
            }
        }

        private static List<S3DownloadResult> CreateTestDownloadResults(int count)
        {
            return CreateTestDownloadResults(count, "test-integration-bucket", "integration-test");
        }

        private static List<S3DownloadResult> CreateTestDownloadResults(int count, string bucketName, string keyPrefix)
        {
            var results = new List<S3DownloadResult>();
            var baseTime = DateTime.UtcNow.AddMinutes(-10);

            for (int i = 0; i < count; i++)
            {
                var timestamp = baseTime.AddSeconds(i * 15);
                var callId = $"CALL_{i / 2:D3}"; // Group events by call
                var eventType = GetEventType(i % 4);

                var xmlContent = CreateSampleLogEventXml(timestamp, callId, eventType);

                results.Add(new S3DownloadResult
                {
                    S3Object = CreateS3ObjectInfo(bucketName, $"{keyPrefix}/event-{i:D3}.xml", xmlContent.Length, timestamp),
                    XmlContent = xmlContent,
                    IsSuccess = true,
                    DownloadTime = DateTime.UtcNow
                });
            }

            return results;
        }

        private static S3ObjectInfo CreateS3ObjectInfo(string bucketName, string objectKey, int size, DateTime eventTime)
        {
            return new S3ObjectInfo
            {
                BucketName = bucketName,
                ObjectKey = objectKey,
                Size = size,
                EventTime = eventTime,
                EventName = "ObjectCreated:Put",
                Region = "us-east-1",
                SqsReceiptHandle = "test-receipt-handle",
                SqsMessageId = "test-message-id"
            };
        }

        private static string CreateComplexS3EventJson()
        {
            var s3Event = new S3EventNotification
            {
                Records = new List<S3EventRecord>()
            };

            for (int i = 0; i < 3; i++)
            {
                s3Event.Records.Add(new S3EventRecord
                {
                    EventVersion = "2.1",
                    EventSource = "aws:s3",
                    AwsRegion = "us-east-1",
                    EventTime = DateTime.UtcNow.AddMinutes(-i),
                    EventName = "ObjectCreated:Put",
                    UserIdentity = new UserIdentity { PrincipalId = $"PRINCIPAL_{i}" },
                    RequestParameters = new RequestParameters { SourceIPAddress = "127.0.0.1" },
                    ResponseElements = new ResponseElements
                    {
                        RequestId = $"REQUEST_{i}",
                        Id2 = $"ID2_{i}"
                    },
                    S3 = new S3Entity
                    {
                        S3SchemaVersion = "1.0",
                        ConfigurationId = $"CONFIG_{i}",
                        Bucket = new S3Bucket
                        {
                            Name = "test-integration-bucket",
                            OwnerIdentity = new OwnerIdentity { PrincipalId = "OWNER_ID" },
                            Arn = "arn:aws:s3:::test-integration-bucket"
                        },
                        Object = new S3Object
                        {
                            Key = $"integration-test/complex-event-{i}.xml",
                            Size = 1500 + (i * 100),
                            ETag = $"etag_{i}",
                            VersionId = $"version_{i}",
                            Sequencer = $"sequencer_{i}"
                        }
                    }
                });
            }

            return JsonConvert.SerializeObject(s3Event, Formatting.Indented);
        }

        private static string GetEventType(int sequence)
        {
            var eventTypes = new[] { "StartCall", "Media", "EndMedia", "EndCall" };
            return eventTypes[sequence % eventTypes.Length];
        }

        private static string CreateSampleLogEventXml(DateTime timestamp, string callId, string eventType)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
                    <LogEvent xmlns=""http://solacom.com/Logging"">
                        <timestamp>{timestamp:yyyy-MM-ddTHH:mm:ss.fffZ}</timestamp>
                        <agencyOrElement>IntegrationTestAgency</agencyOrElement>
                        <agent>Agent001</agent>
                        <callIdentifier>{callId}</callIdentifier>
                        <incidentIdentifier>_II_{callId}</incidentIdentifier>
                        <eventType>{eventType}</eventType>
                    </LogEvent>";
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            Dispose(true);
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _s3FileDownloader?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Collection definition for LocalStack tests to ensure they don't run in parallel
    /// </summary>
    [CollectionDefinition("LocalStack")]
    public class LocalStackCollection : ICollectionFixture<LocalStackFixture>
    {
    }

    /// <summary>
    /// Fixture for LocalStack integration tests
    /// </summary>
    public class LocalStackFixture
    {
        public LocalStackFixture()
        {
            // Could add LocalStack container startup logic here if needed
            // For now, we assumes LocalStack is already running
        }
    }
}
