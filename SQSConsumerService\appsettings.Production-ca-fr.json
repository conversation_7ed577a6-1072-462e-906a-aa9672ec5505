{"Logging": {"LogLevel": {"Default": "Warning", "System": "Error", "Microsoft": "Warning"}, "elasticsearchSettings": {"url": "MtZww66bLovPZnC330l16K7U0U1cMSq6HhMx03lIsIoaHHJ5ZXA1o2/BaaQQQXXNIHDo/9s522Yl5sSnT0f1pk3D7j9Pn62c5/HPbQvknck=", "userName": "MTetEvRDbtRtLZfslf6wYgSu+9aPVPn/Kt0ZZCANfKw=", "password": "ah8QVe9DYkkd5NKg7Vb4Rt+UY9mAt8OdWoStEaP40iQ1ZY6TURtOvWqGyaHgKU4H"}}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "System": "Error"}}, "Properties": {"ApplicationName": "SQSConsumerService", "Instance": "CA-FR", "Environment": "Production-CA-FR"}}, "awsSecretsManager": {"secretName": "prod/ca-fr/csa-sqs-consumer-service/configuration"}, "awsSettings": {"region": "ca-central-1"}}