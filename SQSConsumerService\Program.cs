﻿using System.IO;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Amazon;
using Serilog;
using Serilog.Enrichers.AspnetcoreHttpcontext;
using System;

namespace SQSConsumerService
{
    public static class Program
    {
        public static void Main(string[] args)
        {
            CreateWebHostBuilder(args).Build().Run();
        }

        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    // Add AWS Secrets Manager with auto-polling if enabled
                    var tempConfig = config.Build();
                    var secretsConfig = tempConfig.GetSection("awsSecretsManager");
                    var enabled = secretsConfig.GetValue<bool>("enabled");
                    if (enabled)
                    {
                        var region = secretsConfig.GetValue<string>("region") ?? "us-east-1";
                        var secretPath = secretsConfig.GetValue<string>("secretName");
                        var pollingIntervalMinutes = secretsConfig.GetValue<int>("pollingIntervalMinutes", 240); // Default to 4 hours
                        var useLocalStack = tempConfig.GetSection("awsSettings").GetValue<bool>("useLocalStack");


                        if (!string.IsNullOrEmpty(secretPath))
                        {
                            var serviceUrl = tempConfig.GetSection("awsSettings").GetValue<string>("serviceUrl");
                            var accessKey = tempConfig.GetSection("awsSettings").GetValue<string>("accessKey");
                            var secretKey = tempConfig.GetSection("awsSettings").GetValue<string>("secretKey");

                            Console.WriteLine($"[AWS Secrets Manager] Configuring polling with interval: {pollingIntervalMinutes} minutes for secret: {secretPath}");

                            config.AddSecretsManager(
                                credentials: useLocalStack ? new Amazon.Runtime.BasicAWSCredentials(accessKey, secretKey) : null,
                                region: RegionEndpoint.GetBySystemName(region),
                                configurator: options =>
                                {
                                    options.ConfigureSecretValueRequest = (request, context) =>
                                    {
                                        request.VersionStage = "AWSCURRENT";
                                    };
                                    options.SecretFilter = secret => secret.Name.StartsWith(secretPath);
                                    options.KeyGenerator = (secret, key) => key.Replace($"{secretPath}:", "");
                                    options.PollingInterval = TimeSpan.FromMinutes(pollingIntervalMinutes);
                                    options.ConfigureSecretsManagerConfig = c =>
                                    {
                                        if (useLocalStack)
                                        {
                                            c.ServiceURL = serviceUrl;
                                            c.UseHttp = true;
                                        }
                                    };
                                });
                        }
                    }
                })
                .UseSerilog((provider, hostingContext, loggerConfiguration) =>
                {
                    loggerConfiguration.ReadFrom.Configuration(hostingContext.Configuration)
                        .Enrich.WithAspnetcoreHttpcontext(provider);
                }).UseKestrel()
                .UseContentRoot(Directory.GetCurrentDirectory())
                .UseIISIntegration()
                .UseStartup<Startup>();
    }
}
