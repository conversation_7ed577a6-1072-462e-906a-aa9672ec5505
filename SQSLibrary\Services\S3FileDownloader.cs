using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using Serilog;
using SQSLibrary.Configuration;
using SQSLibrary.Logging;

namespace SQSLibrary.Services
{
    /// <summary>
    /// Service for downloading LogEvent XML files from S3
    /// </summary>
    public class S3FileDownloader : IDisposable
    {
        private static readonly ILogger _logger = LoggerFactory.ForContext<S3FileDownloader>();
        private readonly IAmazonS3 _s3Client;
        private readonly AwsSettings _awsSettings;
        private readonly S3Settings _s3Settings;
        private readonly BatchProcessingSettings _batchSettings;
        private readonly SemaphoreSlim _downloadSemaphore;
        private bool _disposed = false;

        public S3FileDownloader(AwsSettings awsSettings, S3Settings s3Settings, BatchProcessingSettings batchSettings)
        {
            _awsSettings = awsSettings ?? throw new ArgumentNullException(nameof(awsSettings));
            _s3Settings = s3Settings ?? throw new ArgumentNullException(nameof(s3Settings));
            _batchSettings = batchSettings ?? throw new ArgumentNullException(nameof(batchSettings));
            _s3Client = CreateS3Client();
            _downloadSemaphore = new SemaphoreSlim(_batchSettings.MaxConcurrentS3Downloads, _batchSettings.MaxConcurrentS3Downloads);
        }

        /// <summary>
        /// Create S3 client with appropriate configuration optimized for performance
        /// </summary>
        private IAmazonS3 CreateS3Client()
        {
            try
            {
                var config = new AmazonS3Config
                {
                    RegionEndpoint = Amazon.RegionEndpoint.GetBySystemName(_awsSettings.Region),
                    Timeout = TimeSpan.FromSeconds(_s3Settings.DownloadTimeoutSeconds),
                    MaxErrorRetry = _s3Settings.DownloadRetryAttempts,

                    // Performance optimizations
                    UseHttp = _awsSettings.UseLocalStack,
                    RetryMode = Amazon.Runtime.RequestRetryMode.Adaptive
                };

                // Configure for LocalStack if enabled
                if (_awsSettings.UseLocalStack && !string.IsNullOrEmpty(_awsSettings.ServiceUrl))
                {
                    config.ServiceURL = _awsSettings.ServiceUrl;
                    config.UseHttp = true;
                    config.ForcePathStyle = true; // Required for LocalStack

                    _logger.Debug("S3 client configured for LocalStack: {ServiceUrl}", _awsSettings.ServiceUrl);
                    return new AmazonS3Client(_awsSettings.AccessKey, _awsSettings.SecretKey, config);
                }
                else if (!string.IsNullOrEmpty(_awsSettings.AccessKey) && !string.IsNullOrEmpty(_awsSettings.SecretKey))
                {
                    // Use explicit credentials
                    _logger.Debug("S3 client configured with explicit credentials for region {Region}", _awsSettings.Region);
                    return new AmazonS3Client(_awsSettings.AccessKey, _awsSettings.SecretKey, config);
                }
                else
                {
                    // Use default credential chain (IAM roles, etc.)
                    _logger.Debug("S3 client configured with default credential chain for region {Region}", _awsSettings.Region);
                    return new AmazonS3Client(config);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to create S3 client");
                // Re-throw with additional context
                throw;
            }
        }

        /// <summary>
        /// Download LogEvent XML content from multiple S3 objects with optimized parallel processing
        /// </summary>
        /// <param name="s3Objects">List of S3 objects to download</param>
        /// <returns>List of downloaded LogEvent XML content with metadata</returns>
        public async Task<List<S3DownloadResult>> DownloadLogEventsAsync(List<S3ObjectInfo> s3Objects)
        {
            if (s3Objects == null || !s3Objects.Any())
            {
                return new List<S3DownloadResult>();
            }

            var startTime = DateTime.UtcNow;
            _logger.Information("Starting optimized download of {Count} S3 objects (parallel: {ParallelEnabled})",
                s3Objects.Count, _batchSettings.EnableParallelS3Downloads);

            var results = new List<S3DownloadResult>();

            if (_batchSettings.EnableParallelS3Downloads && s3Objects.Count > 1)
            {
                // Optimized parallel downloads with controlled concurrency
                var downloadTasks = s3Objects.Select(s3Object => DownloadSingleObjectAsync(s3Object));
                var downloadResults = await Task.WhenAll(downloadTasks);
                results.AddRange(downloadResults.Where(r => r != null));
            }
            else
            {
                // Sequential downloads for single objects or when parallel is disabled
                foreach (var s3Object in s3Objects)
                {
                    var result = await DownloadSingleObjectAsync(s3Object);
                    if (result != null)
                    {
                        results.Add(result);
                    }
                }
            }

            var elapsed = DateTime.UtcNow - startTime;
            var successCount = results.Count(r => r.IsSuccess);
            var failureCount = results.Count(r => !r.IsSuccess);

            _logger.Information("S3 download completed in {ElapsedMs}ms: {SuccessCount} successful, {FailureCount} failed (avg: {AvgMs:F1}ms per object)",
                elapsed.TotalMilliseconds, successCount, failureCount,
                s3Objects.Count > 0 ? elapsed.TotalMilliseconds / s3Objects.Count : 0);

            return results;
        }

        /// <summary>
        /// Download a single S3 object with optimized retry logic and performance monitoring
        /// </summary>
        /// <param name="s3Object">S3 object to download</param>
        /// <returns>Download result</returns>
        private async Task<S3DownloadResult> DownloadSingleObjectAsync(S3ObjectInfo s3Object)
        {
            await _downloadSemaphore.WaitAsync();
            var downloadStartTime = DateTime.UtcNow;

            try
            {
                for (int attempt = 1; attempt <= _s3Settings.DownloadRetryAttempts; attempt++)
                {
                    try
                    {
                        _logger.Debug("Downloading S3 object {S3Object} (attempt {Attempt}/{MaxAttempts})",
                            s3Object, attempt, _s3Settings.DownloadRetryAttempts);

                        var request = new GetObjectRequest
                        {
                            BucketName = s3Object.BucketName,
                            Key = s3Object.ObjectKey
                        };

                        using var response = await _s3Client.GetObjectAsync(request);
                        using var reader = new StreamReader(response.ResponseStream);
                        var content = await reader.ReadToEndAsync();

                        var downloadTime = DateTime.UtcNow - downloadStartTime;
                        _logger.Debug("Successfully downloaded S3 object {S3Object} in {ElapsedMs}ms ({ContentLength} characters)",
                            s3Object, downloadTime.TotalMilliseconds, content.Length);

                        return new S3DownloadResult
                        {
                            S3Object = s3Object,
                            XmlContent = content,
                            IsSuccess = true,
                            DownloadTime = DateTime.UtcNow
                        };
                    }
                    catch (Exception ex) when (attempt < _s3Settings.DownloadRetryAttempts)
                    {
                        var retryDelay = TimeSpan.FromSeconds(Math.Pow(2, attempt - 1));
                        _logger.Warning(ex, "Failed to download S3 object {S3Object} on attempt {Attempt}, retrying in {RetryDelayMs}ms...",
                            s3Object, attempt, retryDelay.TotalMilliseconds);

                        // Exponential backoff with jitter to avoid thundering herd
                        var jitter = TimeSpan.FromMilliseconds(new Random().Next(0, 1000));
                        await Task.Delay(retryDelay + jitter);
                    }
                    catch (Exception ex)
                    {
                        // Final attempt failed
                        var totalTime = DateTime.UtcNow - downloadStartTime;
                        _logger.Error(ex, "Failed to download S3 object {S3Object} on final attempt {Attempt} after {TotalMs}ms",
                            s3Object, attempt, totalTime.TotalMilliseconds);

                        return new S3DownloadResult
                        {
                            S3Object = s3Object,
                            IsSuccess = false,
                            ErrorMessage = $"Failed on attempt {attempt}: {ex.Message}"
                        };
                    }
                }

                // All attempts failed (shouldn't reach here due to catch above, but safety net)
                var finalTime = DateTime.UtcNow - downloadStartTime;
                _logger.Error("Failed to download S3 object {S3Object} after {Attempts} attempts in {TotalMs}ms",
                    s3Object, _s3Settings.DownloadRetryAttempts, finalTime.TotalMilliseconds);

                return new S3DownloadResult
                {
                    S3Object = s3Object,
                    IsSuccess = false,
                    ErrorMessage = $"Failed after {_s3Settings.DownloadRetryAttempts} attempts"
                };
            }
            finally
            {
                _downloadSemaphore.Release();
            }
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            Dispose(true);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources
                _s3Client?.Dispose();
                _downloadSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Result of downloading an S3 object
    /// </summary>
    public class S3DownloadResult
    {
        public S3ObjectInfo S3Object { get; set; }
        public string XmlContent { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime DownloadTime { get; set; }

        public override string ToString()
        {
            return $"{S3Object} - {(IsSuccess ? "Success" : $"Failed: {ErrorMessage}")}";
        }
    }
}
