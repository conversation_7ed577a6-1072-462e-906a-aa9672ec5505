using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SQSLibrary;
using SQSConsumerService.Code;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Interface for the queue orchestrator that manages SQS consumers and health checking
    /// </summary>
    public interface IQueueOrchestrator : IDisposable
    {
        /// <summary>
        /// Gets the collection of active SQS consumers
        /// </summary>
        IReadOnlyList<SQSManager> ActiveConsumers { get; }

        /// <summary>
        /// Gets the number of configured queues
        /// </summary>
        int ConfiguredQueueCount { get; }

        /// <summary>
        /// Gets the number of healthy consumers
        /// </summary>
        int HealthyConsumerCount { get; }

        /// <summary>
        /// Gets the number of failed consumers
        /// </summary>
        int FailedConsumerCount { get; }

        /// <summary>
        /// Gets the overall health status of all consumers
        /// </summary>
        bool IsHealthy { get; }

        /// <summary>
        /// Gets the overall status (Healthy, Degraded, Unhealthy)
        /// </summary>
        HealthStatus OverallStatus { get; }

        /// <summary>
        /// Initialize the orchestrator with configuration
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <returns>Task representing the initialization operation</returns>
        Task InitializeAsync(IServiceProvider serviceProvider);

        /// <summary>
        /// Start all SQS consumers
        /// </summary>
        /// <returns>Task representing the startup operation</returns>
        Task StartConsumersAsync();

        /// <summary>
        /// Stop all SQS consumers
        /// </summary>
        /// <returns>Task representing the shutdown operation</returns>
        Task StopConsumersAsync();

        /// <summary>
        /// Validate queue configurations - primarily used for testing and diagnostics
        /// </summary>
        /// <returns>Task representing the validation operation</returns>
        Task<bool> ValidateQueuesForTestingAsync();

        /// <summary>
        /// Refresh queue configurations and restart consumers if changes are detected
        /// </summary>
        /// <returns>Task representing the refresh operation, returns true if changes were detected and applied</returns>
        Task<bool> RefreshQueueConfigurationsAsync();

        /// <summary>
        /// Perform cleanup operations
        /// </summary>
        /// <returns>Task representing the cleanup operation</returns>
        Task CleanupAsync();

        /// <summary>
        /// Get basic health information suitable for public endpoints
        /// </summary>
        /// <returns>Health information object without sensitive data</returns>
        object GetHealthDetails();
    }
}
