@echo off
echo -------------------------------------------------------------------
echo          AWS Secrets Manager Configuration Refresh Test
echo -------------------------------------------------------------------
echo This script will test the automatic configuration refresh functionality
echo by updating the secret in LocalStack and verifying the service picks up changes.
echo -------------------------------------------------------------------
echo.

REM Set AWS credentials for LocalStack
set AWS_ACCESS_KEY_ID=test
set AWS_SECRET_ACCESS_KEY=test
set AWS_DEFAULT_REGION=us-east-1
set AWS_ENDPOINT_URL=http://localhost:4566

echo [1/4] Checking LocalStack availability...
curl -f http://localhost:4566/_localstack/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: LocalStack is not ready. Please start it first:
    echo   start-localstack.bat
    exit /b 1
)
echo LocalStack is ready

echo.
echo [2/4] Creating updated secret configuration...

REM Create updated secret with additional queue
echo Creating updated configuration with 3 queues...
(
echo {
echo   "ClientQueues": [
echo     {
echo       "clientCode": "brmb",
echo       "queueName": "local-washingtoncounty-logevents-queue",
echo       "queueUrl": "http://localhost:4566/000000000000/local-washingtoncounty-logevents-queue",
echo       "region": "us-east-1"
echo     },
echo     {
echo       "clientCode": "flfl",
echo       "queueName": "local-flaglercounty-logevents-queue",
echo       "queueUrl": "http://localhost:4566/000000000000/local-flaglercounty-logevents-queue",
echo       "region": "us-east-1"
echo     },
echo     {
echo       "clientCode": "test-client-001",
echo       "queueName": "local-test-client-001-logevents-queue",
echo       "queueUrl": "http://localhost:4566/000000000000/local-test-logevents-queue",
echo       "region": "us-east-1"
echo     }
echo   ]
echo }
) > %TEMP%\updated-secrets-config.json

echo [3/4] Updating secret in LocalStack...
aws secretsmanager update-secret ^
    --secret-id "local/us/csa-sqs-consumer-service/configuration" ^
    --secret-string file://%TEMP%\updated-secrets-config.json ^
    --endpoint-url=%AWS_ENDPOINT_URL%

if %errorlevel% equ 0 (
    echo Secret updated successfully
) else (
    echo Failed to update secret
    exit /b 1
)

echo.
echo [4/4] Cleaning up...
del %TEMP%\updated-secrets-config.json

echo.
echo -------------------------------------------------------------------
echo    Secret Update Complete!
echo -------------------------------------------------------------------
echo.
echo The secret has been updated with 3 queues instead of 2.
echo If you have the SQS Consumer Service running with LocalSecrets environment,
echo it should automatically detect this change and restart the queue orchestrators.
echo.
echo Check the service logs for messages like:
echo   - "Configuration change detected"
echo   - "Restarting queue orchestrators due to configuration change"
echo   - "Successfully loaded 3 client queues from AWS Secrets Manager"
echo.
echo To revert back to original configuration, restart LocalStack:
echo   docker-compose -f docker-compose-localstack.yml restart
echo.
