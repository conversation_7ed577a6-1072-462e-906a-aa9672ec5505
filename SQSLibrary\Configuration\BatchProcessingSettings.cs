namespace SQSLibrary.Configuration
{
    /// <summary>
    /// Batch processing configuration settings
    /// </summary>
    public class BatchProcessingSettings
    {
        /// <summary>
        /// Whether batch processing is enabled
        /// </summary>
        public bool EnableBatchProcessing { get; set; } = true;

        /// <summary>
        /// Maximum number of events in a batch
        /// </summary>
        public int BatchSize { get; set; } = 1;

        /// <summary>
        /// Timeout for batch processing in milliseconds
        /// </summary>
        public int BatchTimeoutMs { get; set; } = 3000;

        /// <summary>
        /// Whether to enable parallel S3 downloads
        /// </summary>
        public bool EnableParallelS3Downloads { get; set; } = true;

        /// <summary>
        /// Maximum number of concurrent S3 downloads
        /// </summary>
        public int MaxConcurrentS3Downloads { get; set; } = 10;
    }
}
