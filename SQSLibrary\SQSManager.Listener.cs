using Amazon.SQS.Model;
using System;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;

namespace SQSLibrary
{
    /// <summary>
    /// SQS Manager Listener functionality
    /// </summary>
    public partial class SQSManager
    {
        /// <summary>
        /// Starts the consumer process to monitor the defined SQS Queue.
        /// Creates SQS client if needed, establishes connection, and begins polling for messages.
        /// </summary>
        /// <param name="collectorURL">The target endpoint of the collector API to process queued events</param>
        /// <returns>Task representing the asynchronous operation</returns>
        /// <exception cref="ArgumentNullException">Thrown when collectorURL is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when queue configuration is invalid</exception>
        /// <exception cref="Amazon.SQS.AmazonSQSException">Thrown when SQS connection fails</exception>
        public async Task StartConsumingAsync(string collectorURL)
        {
            _logger.Information("Starting SQS Consumer for queue {QueueDefinition}", QueueDefinition.ToString());

            if (SqsClient == null)
            {
                CreateSQSClient();
            }

            await GenerateConnectionToQueueAsync();

            // Start the polling task
            _pollingTask = Task.Run(async () => await PollMessagesAsync(collectorURL, _cancellationTokenSource.Token));

            _logger.Information("Connection to SQS queue {QueueDefinition} established successfully, pushing events to {CollectorURL}",
                QueueDefinition.ToString(), collectorURL);
        }

        /// <summary>
        /// Synchronous version of StartConsumingAsync for backward compatibility.
        /// Blocks the calling thread until the consumer is started.
        /// </summary>
        /// <param name="collectorURL">The target endpoint of the collector API to process queued events</param>
        /// <exception cref="ArgumentNullException">Thrown when collectorURL is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when queue configuration is invalid</exception>
        /// <exception cref="Amazon.SQS.AmazonSQSException">Thrown when SQS connection fails</exception>
        public void StartConsuming(string collectorURL)
        {
            StartConsumingAsync(collectorURL).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Continuously polls SQS for messages and processes them.
        /// Runs in a background task until cancellation is requested.
        /// </summary>
        /// <param name="collectorURL">The target endpoint of the collector API to process queued events</param>
        /// <param name="cancellationToken">Token to cancel the polling operation</param>
        /// <returns>Task representing the asynchronous polling operation</returns>
        /// <exception cref="OperationCanceledException">Thrown when cancellation is requested</exception>
        /// <exception cref="Amazon.SQS.AmazonSQSException">Thrown when SQS operations fail</exception>
        /// <returns></returns>
        private async Task PollMessagesAsync(string collectorURL, System.Threading.CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check collector health before processing messages
                    if (_healthChecker != null && !_healthChecker.IsHealthy)
                    {
                        _logger.Warning("Collector API is unhealthy. Waiting for recovery before processing messages...");

                        // Wait for collector to become healthy (with timeout)
                        var healthRestored = await _healthChecker.WaitForHealthyAsync(
                            TimeSpan.FromMinutes(5),
                            cancellationToken);

                        if (!healthRestored)
                        {
                            _logger.Warning("Collector API still unhealthy after 5 minutes. Continuing to wait...");
                            await Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                            continue;
                        }

                        _logger.Information("Collector API health restored. Resuming message processing.");
                    }

                    // Adjust max messages based on batch processing settings
                    int maxMessages = _batchSettings.EnableBatchProcessing ?
                        Math.Min(_batchSettings.BatchSize, 10) : // SQS max is 10
                        _sqsSettings.MaxMessages;

                    var receiveMessageRequest = new ReceiveMessageRequest
                    {
                        QueueUrl = QueueDefinition.QueueUrl,
                        MaxNumberOfMessages = maxMessages,
                        WaitTimeSeconds = _sqsSettings.WaitTimeSeconds,
                        VisibilityTimeout = _sqsSettings.VisibilityTimeoutSeconds
                    };

                    var response = await SqsClient.ReceiveMessageAsync(receiveMessageRequest, cancellationToken);

                    if (response.Messages != null && response.Messages.Any())
                    {
                        _logger.Information("Received {MessageCount} messages from SQS queue {QueueDefinition}", response.Messages.Count, QueueDefinition.ToString());

                        if (_batchSettings.EnableBatchProcessing && _batchProcessor != null)
                        {
                            await ProcessMessagesWithBatching(collectorURL, response.Messages);
                        }
                        else
                        {
                            await ProcessMessagesIndividually(collectorURL, response.Messages);
                        }
                    }

                    // Check if we need to process a batch due to timeout (only when batch processing is enabled)
                    if (_batchSettings.EnableBatchProcessing && _batchProcessor != null && _batchProcessor.IsBatchReady())
                    {
                        await ProcessBatch(collectorURL);
                    }
                }
                catch (OperationCanceledException ex)
                {
                    _logger.Information(ex, "SQS polling cancelled for queue {QueueDefinition}", QueueDefinition.ToString());
                    break;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error polling SQS queue {QueueDefinition}", QueueDefinition.ToString());
                    await Task.Delay(5000, cancellationToken);
                }
            }
        }

        /// <summary>
        /// Process messages individually
        /// </summary>
        /// <param name="collectorURL"></param>
        /// <param name="messages"></param>
        /// <returns></returns>
        private async Task ProcessMessagesIndividually(string collectorURL, List<Message> messages)
        {
            foreach (var message in messages)
            {
                bool isSuccess = await ConsumerOnReceived(collectorURL, message);

                if (isSuccess)
                {
                    // Delete the message from the queue
                    await DeleteMessageAsync(message.ReceiptHandle);
                }
                else
                {
                    // Message will become visible again after visibility timeout
                    _logger.Warning("Failed to process message {MessageId}, will retry after visibility timeout", message.MessageId);
                }
            }
        }

        /// <summary>
        /// Process messages with batching and ordering (S3-based)
        /// </summary>
        /// <param name="collectorURL"></param>
        /// <param name="messages"></param>
        /// <returns></returns>
        private async Task ProcessMessagesWithBatching(string collectorURL, List<Message> messages)
        {
            // Parse S3 event notifications from SQS messages
            var allS3Objects = new List<Services.S3ObjectInfo>();

            foreach (var message in messages)
            {
                var s3Event = Services.S3EventParser.ParseS3EventNotification(message.Body, message.MessageId);
                if (s3Event != null)
                {
                    var s3Objects = Services.S3EventParser.ExtractS3Objects(s3Event, message.ReceiptHandle, message.MessageId);
                    allS3Objects.AddRange(s3Objects);

                    _logger.Debug("Extracted {Count} S3 objects from SQS message {MessageId}",
                        s3Objects.Count, message.MessageId);
                }
                else
                {
                    _logger.Warning("Failed to parse S3 event notification from SQS message {MessageId}", message.MessageId);
                }
            }

            if (allS3Objects.Count > 0)
            {
                // Download LogEvent XML files from S3
                var downloadResults = await _s3FileDownloader.DownloadLogEventsAsync(allS3Objects);

                // Add download results to batch processor
                _batchProcessor.AddS3DownloadResults(downloadResults);

                // Process batch if ready
                if (_batchProcessor.IsBatchReady())
                {
                    await ProcessBatch(collectorURL);
                }
            }
        }

        /// <summary>
        /// Process the current batch
        /// </summary>
        /// <param name="collectorURL"></param>
        /// <returns></returns>
        private async Task ProcessBatch(string collectorURL)
        {
            var orderedEvents = _batchProcessor.GetOrderedBatch();
            if (orderedEvents.Count == 0)
                return;

            var successfulEvents = await BatchProcessor.ProcessOrderedBatch(orderedEvents, collectorURL);

            // Delete successfully processed messages
            foreach (var successfulEvent in successfulEvents)
            {
                await DeleteMessageAsync(successfulEvent.ReceiptHandle);
            }

            // Log failed events (they will become visible again after visibility timeout)
            var failedEvents = orderedEvents.Except(successfulEvents).ToList();
            foreach (var failedEvent in failedEvents)
            {
                _logger.Warning("Failed to process ordered event {MessageId}, will retry after visibility timeout", failedEvent.MessageId);
            }
        }

        /// <summary>
        /// Main process of queue data - retrieving new elements and process action that is taken.
        /// Now handles S3 event notifications instead of direct LogEvent XML.
        /// </summary>
        /// <param name="collectorURL">targeted end point of the collector, includes the full URL including client code and user connection information</param>
        /// <param name="message">SQS Message containing S3 event notification</param>
        /// <returns>success status</returns>
        public virtual async Task<bool> ConsumerOnReceived(string collectorURL, Message message)
        {
            try
            {
                // Parse S3 event notification
                var s3Event = Services.S3EventParser.ParseS3EventNotification(message.Body, message.MessageId);
                if (s3Event == null)
                {
                    _logger.Warning("Failed to parse S3 event notification from SQS message {MessageId}", message.MessageId);
                    return false;
                }

                // Extract S3 objects
                var s3Objects = Services.S3EventParser.ExtractS3Objects(s3Event, message.ReceiptHandle, message.MessageId);
                if (s3Objects.Count == 0)
                {
                    _logger.Warning("No S3 objects found in SQS message {MessageId}", message.MessageId);
                    return false;
                }

                _logger.Debug("Processing {Count} S3 objects from SQS message {MessageId}", s3Objects.Count, message.MessageId);

                // Download and process each S3 object
                var downloadResults = await _s3FileDownloader.DownloadLogEventsAsync(s3Objects);
                bool allSuccessful = true;

                foreach (var result in downloadResults)
                {
                    if (result.IsSuccess)
                    {
                        // Send LogEvent XML directly to Collector API
                        bool success = await PushEvent.ToCollectorAPI(collectorURL, result.XmlContent);
                        if (!success)
                        {
                            allSuccessful = false;
                            _logger.Warning("Failed to send LogEvent from S3 object {S3Object} to Collector API", result.S3Object);
                        }
                        else
                        {
                            _logger.Debug("Successfully sent LogEvent from S3 object {S3Object} to Collector API", result.S3Object);
                        }
                    }
                    else
                    {
                        allSuccessful = false;
                        _logger.Warning("Failed to download S3 object {S3Object}: {ErrorMessage}", result.S3Object, result.ErrorMessage);
                    }
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Exception occurred while processing SQS message {MessageId}", message.MessageId);
                return false;
            }
        }

        /// <summary>
        /// Delete a message from the SQS queue
        /// </summary>
        /// <param name="receiptHandle"></param>
        /// <returns></returns>
        private async Task DeleteMessageAsync(string receiptHandle)
        {
            try
            {
                var deleteMessageRequest = new DeleteMessageRequest
                {
                    QueueUrl = QueueDefinition.QueueUrl,
                    ReceiptHandle = receiptHandle
                };

                await SqsClient.DeleteMessageAsync(deleteMessageRequest);
                _logger.Verbose("Message deleted from SQS queue {QueueDefinition}", QueueDefinition.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to delete message from SQS queue {QueueDefinition}", QueueDefinition.ToString());
            }
        }

        /// <summary>
        /// Stop consuming messages
        /// </summary>
        public void StopConsuming()
        {
            _logger.Information("Stopping SQS consumer for queue {QueueDefinition}", QueueDefinition.ToString());
            _cancellationTokenSource?.Cancel();
        }
    }
}
