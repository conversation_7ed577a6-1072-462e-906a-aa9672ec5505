{"Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "SQSLibrary": "Information", "SQSConsumerService": "Information"}, "elasticsearchSettings": {"url": "zrbdQPOosF6YkSXVAI9nhiCmFhC9ozTB01tnh9mE/fxVvi3fOFgf8ckQmDDfEMnXUAdroMEhIvRzdQMVQzq5A/XGA88j2cGbqsFITfTRksiU6zmbgS8uB9yjLUhZrKW5", "userName": "Ny5ym1gEy7eE5ska83+O9pc5PDUzYw3TeRfP11LDJ8A=", "password": "veZV3sHVDrFH8BFWINgeL2VBuHdUyLPh0+JV+MnW6Hn1TV9/0UMA56blVYFKdQm8"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "SQSLibrary": "Information", "SQSConsumerService": "Information"}}, "Properties": {"ApplicationName": "SQSConsumerService", "Instance": "QA", "Environment": "QA"}}, "awsSecretsManager": {"secretName": "qa/us/csa-sqs-consumer-service/configuration"}}