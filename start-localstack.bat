@echo off
echo -------------------------------------------------------------------
echo          LocalStack S3-Based SQS Consumer - Quick Start
echo -------------------------------------------------------------------
echo This script will:
echo 1. Start LocalStack with S3/SNS/SQS services
echo 2. Set up the complete infrastructure
echo 3. Generate test data
echo 4. Show you how to test the consumer
echo -------------------------------------------------------------------
echo.

REM Check if Docker is available
echo [1/4] Checking Docker availability...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not available. Please install Docker first.
    echo Download from: https://docs.docker.com/get-docker/
    exit /b 1
)
echo Docker is available

REM Check if AWS CLI is available
echo [2/4] Checking AWS CLI availability...
aws --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: AWS CLI is not available. Please install AWS CLI first.
    echo Download from: https://aws.amazon.com/cli/
    exit /b 1
)
echo AWS CLI is available

REM Start LocalStack
echo [3/4] Starting LocalStack...
docker-compose -f docker-compose-localstack.yml up -d
if %errorlevel% neq 0 (
    echo ERROR: Failed to start LocalStack
    exit /b 1
)
echo LocalStack started

echo.
echo -------------------------------------------------------------------
echo    LocalStack Setup Complete!
echo -------------------------------------------------------------------
echo.
echo Next steps:
echo.
echo 1. Generate test data:
echo    scripts\generate-test-data.bat local
echo.
echo 2. Run unit tests:
echo    dotnet test SQSLibrary.Tests
echo.
echo 3. Run integration tests:
echo    dotnet test SQSConsumerService.IntegrationTests
echo.
echo 4. Run the SQS Consumer:
echo    dotnet run --project SQSConsumerService/SQSConsumerService.csproj
echo.
echo 5. Stop LocalStack when done:
echo    docker-compose -f docker-compose-localstack.yml down
echo.
echo -------------------------------------------------------------------
echo    LocalStack URLs:
echo -------------------------------------------------------------------
echo Health Check: http://localhost:4566/_localstack/health
echo -------------------------------------------------------------------
