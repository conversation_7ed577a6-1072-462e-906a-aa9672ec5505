{"Logging": {"LogLevel": {"Default": "Warning", "System": "Error", "Microsoft": "Warning", "SQSLibrary": "Information", "SQSConsumerService": "Information"}, "elasticsearchSettings": {"url": "i4sopP9oudgtPTDFA8JlhQgDAg2nc3o4cVz8BtTmkyPheMnhWZw+7uYXob/VWzSpIT8ECYhTA4qX8DJCgn2igA==", "userName": "QBtL+HQXC8Dhoo4w0wlGxk0N2RExOjUknZYV9d9mmxo=", "password": "PWxm7J5PJjwialxb++D7uKlU334rm0N+MS4b8ZPHXQe0rdcVNW31eg3cRf845Icc"}}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "System": "Error", "SQSLibrary": "Information", "SQSConsumerService": "Information"}}, "Properties": {"ApplicationName": "SQSConsumerService", "Instance": "Production", "Environment": "Production"}}, "awsSecretsManager": {"secretName": "prod/us/csa-sqs-consumer-service/configuration"}}