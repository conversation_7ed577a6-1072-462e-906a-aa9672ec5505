using System;
using System.Security.Cryptography;
using System.IO;

namespace SQSLibrary
{
    public static class Encryption
    {
        /// <summary>
        /// Hardcoded key string for the encryption
        /// </summary>
        /// <remarks>Key length is 256</remarks>
        private static byte[] _privateKey = { 0x25, 0x34, 0x03, 0x90, 0xA5, 0x16, 0x87, 0x08, 0x9F, 0x02, 0xAF, 0xA4, 0x24, 0x10, 0xFA, 0x6C, 0x23, 0x14, 0xA3, 0x9F, 0xA6, 0x16, 0x97, 0x01, 0x2F, 0x0F, 0xBF, 0xA3, 0x27, 0x11, 0xFF, 0x5A };

        /// <summary>
        /// Encryptes a given string
        /// </summary>
        /// <param name="message">Message to encrypt</param>
        /// <returns>Encrypted results</returns>
        public static string Encrypt(string message)
        {
            try
            {
                using var aesAlg = Aes.Create();
                aesAlg.Key = _privateKey;

                var encryptor = aesAlg.CreateEncryptor();

                using var ms = new MemoryStream();

                //Store the generated IV into the resulting encryption string.
                byte[] iv = aesAlg.IV;
                ms.Write(iv, 0, iv.Length);

                using var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write);
                using (var sw = new StreamWriter(cs))
                {
                    sw.Write(message); // Write all data to the stream.
                }
                return Convert.ToBase64String(ms.ToArray());
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Decyrpts a given string
        /// </summary>
        /// <param name="textToDecrypt"></param>
        /// <returns>resulting string.  Returns string.empty if it fails.</returns>
        public static string Decrypt(string textToDecrypt)
        {
            try
            {
                var inputByte = Convert.FromBase64String(textToDecrypt);
                using var ms = new MemoryStream(inputByte);

                using var aesAlg = Aes.Create();

                byte[] iv = new byte[aesAlg.IV.Length];
                var bytesRead = ms.Read(iv, 0, iv.Length);
                if (bytesRead != iv.Length)
                {
                    throw new InvalidOperationException("Failed to read complete IV from encrypted data");
                }

                var decryptor = aesAlg.CreateDecryptor(_privateKey, iv);

                using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
                using var sr = new StreamReader(cs);
                return sr.ReadToEnd();
            }
            catch (Exception)
            {
                //return the original string, when not encrypted to allow for normal operation
                return textToDecrypt;
            }
        }

    }
}
