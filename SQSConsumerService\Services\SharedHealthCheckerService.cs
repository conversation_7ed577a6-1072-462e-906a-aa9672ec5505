using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SQSLibrary.Services;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Shared health checker service that manages a single health checker instance
    /// for all SQS consumers to avoid duplicate health checks
    /// </summary>
    public class SharedHealthCheckerService : ISharedHealthCheckerService, IDisposable
    {
        private readonly ILogger<SharedHealthCheckerService> _logger;
        private CollectorHealthChecker _healthChecker;
        private bool _disposed = false;
        private readonly object _lock = new object();

        public SharedHealthCheckerService(ILogger<SharedHealthCheckerService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Gets the current health status of the collector API
        /// </summary>
        public bool IsHealthy
        {
            get
            {
                lock (_lock)
                {
                    return _healthChecker?.IsHealthy ?? false;
                }
            }
        }

        /// <summary>
        /// Gets the number of consecutive failures
        /// </summary>
        public int ConsecutiveFailures
        {
            get
            {
                lock (_lock)
                {
                    return _healthChecker?.ConsecutiveFailures ?? 0;
                }
            }
        }

        /// <summary>
        /// Initialize the health checker with the given settings
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown when service is disposed</exception>
        public void Initialize(string healthCheckUrl, TimeSpan healthCheckInterval, TimeSpan healthCheckTimeout, int maxConsecutiveFailures)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SharedHealthCheckerService));

            lock (_lock)
            {
                if (_healthChecker != null)
                {
                    _logger.LogDebug("Health checker already initialized for URL: {HealthCheckUrl}", healthCheckUrl);
                    return;
                }

                _logger.LogInformation("Initializing shared health checker for URL: {HealthCheckUrl} with {IntervalMs}ms interval", 
                    healthCheckUrl, healthCheckInterval.TotalMilliseconds);

                _healthChecker = new CollectorHealthChecker(
                    healthCheckUrl,
                    healthCheckInterval,
                    healthCheckTimeout,
                    maxConsecutiveFailures);
            }
        }

        /// <summary>
        /// Manually trigger a health check
        /// </summary>
        public async Task<bool> CheckHealthAsync()
        {
            if (_disposed)
                return false;

            CollectorHealthChecker checker;
            lock (_lock)
            {
                checker = _healthChecker;
            }

            if (checker == null)
            {
                _logger.LogWarning("Health checker not initialized");
                return false;
            }

            return await checker.CheckHealthAsync();
        }

        /// <summary>
        /// Wait for the collector API to become healthy
        /// </summary>
        public async Task<bool> WaitForHealthyAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            if (_disposed)
                return false;

            CollectorHealthChecker checker;
            lock (_lock)
            {
                checker = _healthChecker;
            }

            if (checker == null)
            {
                _logger.LogWarning("Health checker not initialized");
                return false;
            }

            return await checker.WaitForHealthyAsync(timeout, cancellationToken);
        }

        /// <summary>
        /// Stop the health checker
        /// </summary>
        public void Stop()
        {
            lock (_lock)
            {
                if (_healthChecker != null)
                {
                    _logger.LogInformation("Stopping shared health checker");
                    _healthChecker.Dispose();
                    _healthChecker = null;
                }
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                Stop();
                _disposed = true;
            }
        }
    }
}
