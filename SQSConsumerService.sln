﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.29503.13
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SQSConsumerService", "SQSConsumerService\SQSConsumerService.csproj", "{DDBCF19D-1B24-4E53-9E59-44D006812D50}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SQSConsumerService.IntegrationTests", "SQSConsumerService.IntegrationTests\SQSConsumerService.IntegrationTests.csproj", "{986BDAB7-B9E7-4E7E-9755-28635F08B19E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SQSConsumerService.Tests", "SQSConsumerService.Tests\SQSConsumerService.Tests.csproj", "{C3D4E5F6-7890-1234-BCDE-F23456789012}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SQSLibrary", "SQSLibrary\SQSLibrary.csproj", "{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SQSLibrary.Tests", "SQSLibrary.Tests\SQSLibrary.Tests.csproj", "{B2C3D4E5-6F78-9012-BCDE-F23456789012}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Debug|x64.Build.0 = Debug|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Debug|x86.Build.0 = Debug|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Release|x64.ActiveCfg = Release|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Release|x64.Build.0 = Release|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Release|x86.ActiveCfg = Release|Any CPU
		{DDBCF19D-1B24-4E53-9E59-44D006812D50}.Release|x86.Build.0 = Release|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Debug|x64.Build.0 = Debug|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Debug|x86.Build.0 = Debug|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Release|Any CPU.Build.0 = Release|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Release|x64.ActiveCfg = Release|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Release|x64.Build.0 = Release|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Release|x86.ActiveCfg = Release|Any CPU
		{986BDAB7-B9E7-4E7E-9755-28635F08B19E}.Release|x86.Build.0 = Release|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Debug|x64.Build.0 = Debug|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Debug|x86.Build.0 = Debug|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Release|x64.ActiveCfg = Release|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Release|x64.Build.0 = Release|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Release|x86.ActiveCfg = Release|Any CPU
		{C3D4E5F6-7890-1234-BCDE-F23456789012}.Release|x86.Build.0 = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Debug|x64.Build.0 = Debug|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Debug|x86.Build.0 = Debug|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Release|x64.ActiveCfg = Release|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Release|x64.Build.0 = Release|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Release|x86.ActiveCfg = Release|Any CPU
		{B2C3D4E5-6F78-9012-BCDE-F23456789012}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BE968FD2-579E-4471-B2E9-120D14E13CB9}
	EndGlobalSection
EndGlobal
