namespace SQSLibrary.Configuration
{

    /// <summary>
    /// Defines the AWS SQS configuration settings
    /// </summary>
    public class SQSSettings
    {
        

        
        /// <summary>
        /// Maximum number of messages to receive in a single poll (1-10)
        /// </summary>
        public int MaxMessages { get; set; } = 1;

        /// <summary>
        /// Wait time for long polling in seconds (0-20)
        /// </summary>
        public int WaitTimeSeconds { get; set; } = 20;

        /// <summary>
        /// Message visibility timeout in seconds
        /// </summary>
        public int VisibilityTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// To String override for easy debugging information
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"MaxMessages={MaxMessages}, WaitTime={WaitTimeSeconds}s, VisibilityTimeout={VisibilityTimeoutSeconds}s";
        }
    }
}
