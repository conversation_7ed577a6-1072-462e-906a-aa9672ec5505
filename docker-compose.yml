# SQS Consumer Service - Docker Compose Configuration
# This file uses environment variables from .env file

services:
  sqs-consumer-service:
    build:
      context: .
      dockerfile: SQSConsumerService/Dockerfile
    image: sqs-consumer-service:${IMAGE_TAG:-latest}
    container_name: sqs-consumer-service
    ports:
      - "${HTTP_PORT:-8080}:80"
    environment:
      # Core Application Settings
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production-ca}
      - ASPNETCORE_URLS=http://+:80

      # AWS Settings
      - awsSettings__useLocalStack=false
      - awsSettings__region=${AWS_REGION:-ca-central-1}

      # SQS Settings
      - sqsSettings__maxMessages=${SQS_MAX_MESSAGES:-1}
      - sqsSettings__waitTimeSeconds=${SQS_WAIT_TIME_SECONDS:-20}
      - sqsSettings__visibilityTimeoutSeconds=${SQS_VISIBILITY_TIMEOUT_SECONDS:-30}

      # S3 Settings
      - s3Settings__downloadTimeoutSeconds=${S3_DOWNLOAD_TIMEOUT_SECONDS:-30}
      - s3Settings__downloadRetryAttempts=${S3_DOWNLOAD_RETRY_ATTEMPTS:-3}

      # Batch Processing Settings
      - batchProcessingSettings__enableBatchProcessing=${BATCH_PROCESSING_ENABLED:-true}
      - batchProcessingSettings__batchSize=${BATCH_SIZE:-5}
      - batchProcessingSettings__batchTimeoutMs=${BATCH_TIMEOUT_MS:-2000}
      - batchProcessingSettings__enableParallelS3Downloads=${PARALLEL_S3_DOWNLOADS_ENABLED:-true}
      - batchProcessingSettings__maxConcurrentS3Downloads=${MAX_CONCURRENT_S3_DOWNLOADS:-20}

      # Collector API Settings
      - collectorAPISettings__baseUrl=${COLLECTOR_API_BASE_URL:-http://prod-ca-comtech-smartanalytics-api.nsoc.state911.net}
      - collectorAPISettings__eventsEndpoint=${COLLECTOR_API_EVENTS_ENDPOINT:-/api/events/{clientCode}}
      - collectorAPISettings__healthEndpoint=${COLLECTOR_API_HEALTH_ENDPOINT:-/api/health/ping}
      - collectorAPISettings__userId=${COLLECTOR_API_USER_ID:-14c/bpnwiR8rgjvUyzc6QHcnSztStAhgtUMBu0YxulgXqsi+mNgMB/fxPJy0U5wrXri7MBz40CqvJyvBX253/g==}
      - collectorAPISettings__timeoutSeconds=${COLLECTOR_API_TIMEOUT_SECONDS:-30}

      # Health Check Settings
      - healthCheckSettings__enabled=${HEALTH_CHECK_ENABLED:-true}
      - healthCheckSettings__intervalSeconds=${HEALTH_CHECK_INTERVAL_SECONDS:-30}
      - healthCheckSettings__timeoutSeconds=${HEALTH_CHECK_TIMEOUT_SECONDS:-5}
      - healthCheckSettings__maxConsecutiveFailures=${HEALTH_CHECK_MAX_CONSECUTIVE_FAILURES:-2}

      # AWS Secrets Manager
      - awsSecretsManager__enabled=${AWS_SECRETS_MANAGER_ENABLED:-false}
      - awsSecretsManager__secretName=${AWS_SECRETS_MANAGER_SECRET_NAME:-}
      - awsSecretsManager__region=${AWS_SECRETS_MANAGER_REGION:-ca-central-1}
      - awsSecretsManager__fallbackToLocal=${AWS_SECRETS_MANAGER_FALLBACK_TO_LOCAL:-true}
      - awsSecretsManager__pollingIntervalMinutes=${AWS_SECRETS_MANAGER_POLLING_INTERVAL_MINUTES:-240}

      # Logging
      - Logging__LogLevel__Default=${LOG_LEVEL_DEFAULT:-Information}
      - Logging__LogLevel__SQSLibrary=${LOG_LEVEL_SQSLIBRARY:-Information}
      - Logging__LogLevel__SQSConsumerService=${LOG_LEVEL_SQSCONSUMERSERVICE:-Information}
      - Logging__LogLevel__Microsoft=${LOG_LEVEL_MICROSOFT:-Warning}
      - Logging__LogLevel__System=${LOG_LEVEL_SYSTEM:-Warning}
    volumes:
      - "${LOG_VOLUME_PATH:-./logs}:/app/logs"
    networks:
      - sqs-consumer-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/api/health/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

networks:
  sqs-consumer-network:
    driver: bridge
    name: sqs-consumer-network
