using System;
using System.Threading;
using System.Threading.Tasks;

namespace SQSLibrary.Services
{
    /// <summary>
    /// Interface for health checking services
    /// </summary>
    public interface IHealthChecker
    {
        /// <summary>
        /// Gets the current health status
        /// </summary>
        bool <PERSON><PERSON>eal<PERSON> { get; }

        /// <summary>
        /// Gets the number of consecutive failures
        /// </summary>
        int ConsecutiveFailures { get; }

        /// <summary>
        /// Manually trigger a health check (non-blocking)
        /// </summary>
        /// <returns>Current health status</returns>
        Task<bool> CheckHealthAsync();

        /// <summary>
        /// Wait for the service to become healthy
        /// </summary>
        /// <param name="timeout">Maximum time to wait</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if healthy within timeout, false otherwise</returns>
        Task<bool> WaitForHealthyAsync(TimeSpan timeout, CancellationToken cancellationToken = default);
    }
}
