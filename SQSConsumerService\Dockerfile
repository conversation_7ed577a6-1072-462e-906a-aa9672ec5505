FROM mcr.microsoft.com/dotnet/core/aspnet:3.1-buster-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install curl for health checks and create non-root user for security
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/* && \
    groupadd -r appuser && useradd -r -g appuser appuser && \
    mkdir -p /app/logs && \
    chown -R appuser:appuser /app

FROM mcr.microsoft.com/dotnet/core/sdk:3.1-buster AS build
WORKDIR /src

COPY ["SQSConsumerService/SQSConsumerService.csproj", "SQSConsumerService/"]
COPY ["SQSLibrary/SQSLibrary.csproj", "SQSLibrary/"]
RUN dotnet restore "SQSConsumerService/SQSConsumerService.csproj"

# Copy source code and build
COPY . .
WORKDIR "/src/SQSConsumerService"
RUN dotnet build "SQSConsumerService.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SQSConsumerService.csproj" -c Release -o /app/publish --no-restore

# Final stage
FROM base AS final
WORKDIR /app

COPY --from=publish /app/publish .

# Switch to non-root user
USER appuser

# Set default environment variables
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:80/api/health/ping || exit 1

ENTRYPOINT ["dotnet", "SQSConsumerService.dll"]