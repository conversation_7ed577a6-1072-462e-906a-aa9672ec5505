using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Serilog;
using SQSLibrary.Models;

namespace SQSLibrary.Services
{
    /// <summary>
    /// Service for parsing S3 event notifications from SQS messages
    /// </summary>
    public static class S3EventParser
    {
        /// <summary>
        /// Parse S3 event notification from SQS message body
        /// </summary>
        /// <param name="messageBody">SQS message body containing S3 event notification</param>
        /// <param name="messageId">SQS message ID for logging</param>
        /// <returns>Parsed S3 event notification or null if parsing fails</returns>
        public static S3EventNotification ParseS3EventNotification(string messageBody, string messageId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(messageBody))
                {
                    Log.Logger.Warning("Empty message body for message {MessageId}", messageId);
                    return null;
                }

                // Log the raw message body for debugging LocalStack format
                Log.Logger.Debug("Raw message body for {MessageId}: {MessageBody}", messageId, messageBody);

                // Try to parse as SNS notification first (for LocalStack and SNS-based setups)
                var snsNotification = TryParseSnsNotification(messageBody);
                if (snsNotification != null)
                {
                    Log.Logger.Debug("Detected SNS notification, extracting S3 event from Message field for {MessageId}", messageId);
                    messageBody = snsNotification.Message;
                }

                // Parse JSON to S3 event notification
                var s3Event = JsonConvert.DeserializeObject<S3EventNotification>(messageBody);
                
                if (s3Event?.Records == null || !s3Event.Records.Any())
                {
                    Log.Logger.Warning("No S3 records found in message {MessageId}", messageId);
                    return null;
                }

                // Validate that records have valid S3 data
                var invalidRecords = s3Event.Records.Where(r => !IsValidS3Record(r)).ToList();
                if (invalidRecords.Any())
                {
                    Log.Logger.Warning("Found {Count} invalid S3 records in message {MessageId}",
                        invalidRecords.Count, messageId);
                }

                // Filter to only valid S3 events
                s3Event.Records = s3Event.Records.Where(r => IsValidS3Record(r)).ToList();

                Log.Logger.Debug("Successfully parsed {Count} S3 records from message {MessageId}", 
                    s3Event.Records.Count, messageId);

                return s3Event;
            }
            catch (JsonException ex)
            {
                Log.Logger.Error(ex, "Failed to parse S3 event notification JSON for message {MessageId}", messageId);
                return null;
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Unexpected error parsing S3 event notification for message {MessageId}", messageId);
                return null;
            }
        }

        /// <summary>
        /// Extract S3 object information from S3 event records
        /// </summary>
        /// <param name="s3Event">Parsed S3 event notification</param>
        /// <param name="sqsReceiptHandle">SQS receipt handle for message deletion</param>
        /// <param name="sqsMessageId">SQS message ID for tracking</param>
        /// <returns>List of S3 object information for downloading</returns>
        public static List<S3ObjectInfo> ExtractS3Objects(S3EventNotification s3Event, string sqsReceiptHandle = null, string sqsMessageId = null)
        {
            if (s3Event?.Records == null)
            {
                return new List<S3ObjectInfo>();
            }

            var s3Objects = new List<S3ObjectInfo>();

            foreach (var record in s3Event.Records)
            {
                try
                {
                    if (record.S3?.Bucket?.Name == null || record.S3?.Object?.Key == null)
                    {
                        Log.Logger.Warning("Invalid S3 record: missing bucket name or object key");
                        continue;
                    }

                    // URL decode the object key (S3 keys are URL encoded in notifications)
                    var decodedKey = System.Net.WebUtility.UrlDecode(record.S3.Object.Key);

                    var s3ObjectInfo = new S3ObjectInfo
                    {
                        BucketName = record.S3.Bucket.Name,
                        ObjectKey = decodedKey,
                        Size = record.S3.Object.Size,
                        ETag = record.S3.Object.ETag,
                        EventTime = record.EventTime,
                        EventName = record.EventName,
                        Region = record.AwsRegion,
                        SqsReceiptHandle = sqsReceiptHandle,
                        SqsMessageId = sqsMessageId
                    };

                    s3Objects.Add(s3ObjectInfo);
                    
                    Log.Logger.Debug("Extracted S3 object: {BucketName}/{ObjectKey} ({Size} bytes)", 
                        s3ObjectInfo.BucketName, s3ObjectInfo.ObjectKey, s3ObjectInfo.Size);
                }
                catch (Exception ex)
                {
                    Log.Logger.Error(ex, "Error extracting S3 object from record");
                }
            }

            return s3Objects;
        }

        /// <summary>
        /// Validate if a record is a valid S3 event record
        /// </summary>
        /// <param name="record">S3 event record to validate</param>
        /// <returns>True if the record is valid, false otherwise</returns>
        private static bool IsValidS3Record(S3EventRecord record)
        {
            if (record == null)
                return false;

            if (record.S3?.Bucket?.Name != null && record.S3?.Object?.Key != null)
            {
                // Check if the bucket name and object key are not empty
                return !string.IsNullOrWhiteSpace(record.S3.Bucket.Name) &&
                       !string.IsNullOrWhiteSpace(record.S3.Object.Key);
            }

            return false;
        }

        /// <summary>
        /// Try to parse message as SNS notification
        /// </summary>
        /// <param name="messageBody">Raw message body</param>
        /// <returns>SNS notification if successful, null otherwise</returns>
        private static SnsNotification TryParseSnsNotification(string messageBody)
        {
            try
            {
                // Check if this looks like an SNS notification
                if (!messageBody.Contains("\"Type\"") || !messageBody.Contains("\"Message\""))
                {
                    return null;
                }

                var snsNotification = JsonConvert.DeserializeObject<SnsNotification>(messageBody);

                // Validate it's a proper SNS notification
                if (snsNotification?.Type == "Notification" && !string.IsNullOrWhiteSpace(snsNotification.Message))
                {
                    return snsNotification;
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Logger.Debug(ex, "Failed to parse as SNS notification");
                return null;
            }
        }
    }

    /// <summary>
    /// SNS Notification structure for LocalStack and SNS-based S3 events
    /// </summary>
    public class SnsNotification
    {
        public string Type { get; set; }
        public string MessageId { get; set; }
        public string TopicArn { get; set; }
        public string Message { get; set; }
        public string Timestamp { get; set; }
        public string SignatureVersion { get; set; }
        public string Signature { get; set; }
        public string SigningCertURL { get; set; }
        public string UnsubscribeURL { get; set; }
        public string Subject { get; set; }
    }

    /// <summary>
    /// Information about an S3 object to be downloaded
    /// </summary>
    public class S3ObjectInfo
    {
        public string BucketName { get; set; }
        public string ObjectKey { get; set; }
        public long Size { get; set; }
        public string ETag { get; set; }
        public DateTime EventTime { get; set; }
        public string EventName { get; set; }
        public string Region { get; set; }

        /// <summary>
        /// SQS receipt handle for the message that contained this S3 event
        /// Required for proper message deletion after processing
        /// </summary>
        public string SqsReceiptHandle { get; set; }

        /// <summary>
        /// SQS message ID for tracking and logging
        /// </summary>
        public string SqsMessageId { get; set; }

        public override string ToString()
        {
            return $"s3://{BucketName}/{ObjectKey} ({Size} bytes)";
        }
    }
}
