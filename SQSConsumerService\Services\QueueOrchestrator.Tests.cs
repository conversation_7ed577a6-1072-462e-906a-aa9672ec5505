using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace SQSConsumerService.Services
{
    /// <summary>
    /// Partial class containing test-specific and validation methods for QueueOrchestrator
    /// </summary>
    public partial class QueueOrchestrator
    {
        #region Test and Validation Methods

        /// <summary>
        /// Validate queue configurations - primarily used for testing and diagnostics
        /// This method creates temporary connections to verify queue accessibility
        /// </summary>
        /// <returns>True if all configured queues are accessible, false otherwise</returns>
        /// <exception cref="InvalidOperationException">Thrown when orchestrator is not initialized</exception>
        public async Task<bool> ValidateQueuesForTestingAsync()
        {
            if (!_initialized)
                throw new InvalidOperationException("Queue orchestrator must be initialized before validating queues");

            _logger.LogInformation("Validating queue configurations for testing...");

            var validationResults = new List<bool>();

            foreach (var queue in _configuredQueues)
            {
                try
                {
                    using var tempManager = CreateSQSManager(queue);
                    await tempManager.GenerateConnectionToQueueAsync();
                    validationResults.Add(true);
                    _logger.LogDebug("Queue validation successful for: {QueueName}", queue.QueueName);
                }
                catch (Exception ex)
                {
                    validationResults.Add(false);
                    _logger.LogError(ex, "Queue validation failed for: {QueueName}", queue.QueueName);
                }
            }

            var allValid = validationResults.All(r => r);
            _logger.LogInformation("Queue validation completed. Valid: {ValidCount}/{TotalCount}", 
                validationResults.Count(r => r), validationResults.Count);

            return allValid;
        }

        #endregion
    }
}
