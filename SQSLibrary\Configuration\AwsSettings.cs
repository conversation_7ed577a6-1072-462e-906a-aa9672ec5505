namespace SQSLibrary.Configuration
{
    /// <summary>
    /// AWS configuration settings
    /// </summary>
    public class AwsSettings
    {
        /// <summary>
        /// AWS region
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// Whether to use LocalStack for local development
        /// </summary>
        public bool UseLocalStack { get; set; }

        /// <summary>
        /// LocalStack service URL (only used when UseLocalStack is true)
        /// </summary>
        public string ServiceUrl { get; set; }

        /// <summary>
        /// AWS access key (only for LocalStack, production should use IAM roles)
        /// </summary>
        public string AccessKey { get; set; }

        /// <summary>
        /// AWS secret key (only for LocalStack, production should use IAM roles)
        /// </summary>
        public string SecretKey { get; set; }
    }
}
