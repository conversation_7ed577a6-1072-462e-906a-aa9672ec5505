using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using SQSConsumerService.Services;
using SQSConsumerService.Code;
using SQSLibrary;
using SQSLibrary.Configuration;

namespace SQSConsumerService.IntegrationTests
{
    public class QueueOrchestratorIntegrationTests : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;
        private readonly IQueueOrchestrator _orchestrator;

        public QueueOrchestratorIntegrationTests()
        {
            // Build configuration from test settings
            var configBuilder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables();

            _configuration = configBuilder.Build();

            // Build service collection
            var services = new ServiceCollection();
            
            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            // Add configuration
            services.AddSingleton(_configuration);

            // Add configuration objects as singletons
            var awsSettings = new AwsSettings();
            _configuration.GetSection("awsSettings").Bind(awsSettings);
            services.AddSingleton(awsSettings);

            var sqsSettings = new SQSSettings();
            _configuration.GetSection("sqsSettings").Bind(sqsSettings);
            services.AddSingleton(sqsSettings);

            var s3Settings = new S3Settings();
            _configuration.GetSection("s3Settings").Bind(s3Settings);
            services.AddSingleton(s3Settings);

            var batchSettings = new BatchProcessingSettings();
            _configuration.GetSection("batchProcessingSettings").Bind(batchSettings);
            services.AddSingleton(batchSettings);

            var healthSettings = new HealthCheckSettings();
            _configuration.GetSection("healthCheckSettings").Bind(healthSettings);
            services.AddSingleton(healthSettings);

            var collectorAPISettings = new CollectorAPISettings();
            _configuration.GetSection("collectorAPISettings").Bind(collectorAPISettings);
            services.AddSingleton(collectorAPISettings);

            // Register configuration change notification services
            services.AddSingleton<IConfigurationChangeNotificationService, ConfigurationChangeNotificationService>();

            services.AddSingleton<ISharedHealthCheckerService, SharedHealthCheckerService>();
            services.AddSingleton<IQueueOrchestrator, QueueOrchestrator>();

            _serviceProvider = services.BuildServiceProvider();
            _orchestrator = _serviceProvider.GetRequiredService<IQueueOrchestrator>();
        }

        public void Dispose()
        {
            _orchestrator?.Dispose();
            (_serviceProvider as IDisposable)?.Dispose();
        }

        [Fact]
        public async Task QueueOrchestrator_FullLifecycle_ShouldWorkCorrectly()
        {
            // Act - Initialize
            await _orchestrator.InitializeAsync(_serviceProvider);

            // Assert - Initialization
            Assert.True(_orchestrator.ConfiguredQueueCount > 0);
            Assert.Equal(HealthStatus.Unhealthy, _orchestrator.OverallStatus); // No consumers started yet

            // Act - Validate queues (this will fail with LocalStack not running, but should not throw)
            await _orchestrator.ValidateQueuesForTestingAsync();

            // Act - Get health details
            var healthDetails = _orchestrator.GetHealthDetails();

            // Assert - Health details
            Assert.NotNull(healthDetails);

            // Act - Cleanup
            await _orchestrator.CleanupAsync();

            // Assert - Cleanup completed without errors
            Assert.True(true); // Full lifecycle completed successfully
        }

        [Fact]
        public async Task QueueOrchestrator_InitializeTwice_ShouldNotFail()
        {
            // Act
            await _orchestrator.InitializeAsync(_serviceProvider);
            await _orchestrator.InitializeAsync(_serviceProvider); // Second call should be ignored

            // Assert
            Assert.True(_orchestrator.ConfiguredQueueCount > 0);
        }

        [Fact]
        public void QueueOrchestrator_HealthProperties_ShouldReturnValidValues()
        {
            // Act
            var activeConsumers = _orchestrator.ActiveConsumers.Count;
            var configuredCount = _orchestrator.ConfiguredQueueCount;
            var healthyCount = _orchestrator.HealthyConsumerCount;
            var failedCount = _orchestrator.FailedConsumerCount;
            var isHealthy = _orchestrator.IsHealthy;
            var overallStatus = _orchestrator.OverallStatus;

            // Assert
            Assert.True(activeConsumers >= 0);
            Assert.True(configuredCount >= 0);
            Assert.True(healthyCount >= 0);
            Assert.True(failedCount >= 0);
            Assert.IsType<bool>(isHealthy);
            Assert.IsType<HealthStatus>(overallStatus);
            Assert.True(Enum.IsDefined(typeof(HealthStatus), overallStatus));
        }

        [Fact]
        public async Task QueueOrchestrator_StartConsumersWithoutValidQueues_ShouldHandleGracefully()
        {
            // Arrange
            await _orchestrator.InitializeAsync(_serviceProvider);

            // Act & Assert - Should not throw even if queues don't exist
            await _orchestrator.StartConsumersAsync(); // Should complete without throwing
            Assert.True(true); // Completed without exceptions
        }
    }
}
