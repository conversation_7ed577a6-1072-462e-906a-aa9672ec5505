using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace SQSLibrary.Models
{
    /// <summary>
    /// Represents an S3 Event Notification received via SQS
    /// </summary>
    public class S3EventNotification
    {
        [JsonProperty("Records")]
        public List<S3EventRecord> Records { get; set; } = new List<S3EventRecord>();
    }

    /// <summary>
    /// Represents a single S3 event record
    /// </summary>
    public class S3EventRecord
    {
        [JsonProperty("eventVersion")]
        public string EventVersion { get; set; }

        [JsonProperty("eventSource")]
        public string EventSource { get; set; }

        [JsonProperty("awsRegion")]
        public string AwsRegion { get; set; }

        [JsonProperty("eventTime")]
        public DateTime EventTime { get; set; }

        [JsonProperty("eventName")]
        public string EventName { get; set; }

        [JsonProperty("userIdentity")]
        public UserIdentity UserIdentity { get; set; }

        [JsonProperty("requestParameters")]
        public RequestParameters RequestParameters { get; set; }

        [JsonProperty("responseElements")]
        public ResponseElements ResponseElements { get; set; }

        [JsonProperty("s3")]
        public S3Entity S3 { get; set; }
    }

    /// <summary>
    /// User identity information
    /// </summary>
    public class UserIdentity
    {
        [JsonProperty("principalId")]
        public string PrincipalId { get; set; }
    }

    /// <summary>
    /// Request parameters
    /// </summary>
    public class RequestParameters
    {
        [JsonProperty("sourceIPAddress")]
        public string SourceIPAddress { get; set; }
    }

    /// <summary>
    /// Response elements
    /// </summary>
    public class ResponseElements
    {
        [JsonProperty("x-amz-request-id")]
        public string RequestId { get; set; }

        [JsonProperty("x-amz-id-2")]
        public string Id2 { get; set; }
    }

    /// <summary>
    /// S3 entity information
    /// </summary>
    public class S3Entity
    {
        [JsonProperty("s3SchemaVersion")]
        public string S3SchemaVersion { get; set; }

        [JsonProperty("configurationId")]
        public string ConfigurationId { get; set; }

        [JsonProperty("bucket")]
        public S3Bucket Bucket { get; set; }

        [JsonProperty("object")]
        public S3Object Object { get; set; }
    }

    /// <summary>
    /// S3 bucket information
    /// </summary>
    public class S3Bucket
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("ownerIdentity")]
        public OwnerIdentity OwnerIdentity { get; set; }

        [JsonProperty("arn")]
        public string Arn { get; set; }
    }

    /// <summary>
    /// Owner identity information
    /// </summary>
    public class OwnerIdentity
    {
        [JsonProperty("principalId")]
        public string PrincipalId { get; set; }
    }

    /// <summary>
    /// S3 object information
    /// </summary>
    public class S3Object
    {
        [JsonProperty("key")]
        public string Key { get; set; }

        [JsonProperty("size")]
        public long Size { get; set; }

        [JsonProperty("eTag")]
        public string ETag { get; set; }

        [JsonProperty("versionId")]
        public string VersionId { get; set; }

        [JsonProperty("sequencer")]
        public string Sequencer { get; set; }
    }
}
