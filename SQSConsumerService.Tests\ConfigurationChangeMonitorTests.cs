using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Moq;
using SQSConsumerService.Services;
using SQSLibrary;
using Xunit;

namespace SQSConsumerService.Tests
{
    /// <summary>
    /// Unit tests for ConfigurationChangeMonitor
    /// </summary>
    public class ConfigurationChangeMonitorTests : IDisposable
    {
        private readonly Mock<ILogger<ConfigurationChangeMonitor>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<IConfigurationChangeNotificationService> _mockNotificationService;
        private readonly ConfigurationChangeMonitorSettings _settings;
        private readonly ConfigurationChangeMonitor _monitor;

        public ConfigurationChangeMonitorTests()
        {
            _mockLogger = new Mock<ILogger<ConfigurationChangeMonitor>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockNotificationService = new Mock<IConfigurationChangeNotificationService>();
            _settings = new ConfigurationChangeMonitorSettings
            {
                Enabled = true,
                DebounceDelayMs = 100 // Short delay for testing
            };

            // Setup mock configuration to return a valid change token
            var mockChangeToken = new Mock<IChangeToken>();
            _mockConfiguration.Setup(c => c.GetReloadToken()).Returns(mockChangeToken.Object);

            // Setup mock configuration section for ClientQueues
            var mockClientQueuesSection = new Mock<IConfigurationSection>();
            mockClientQueuesSection.Setup(s => s.Value).Returns("[]");
            mockClientQueuesSection.Setup(s => s.GetChildren()).Returns(new List<IConfigurationSection>());
            _mockConfiguration.Setup(c => c.GetSection("ClientQueues")).Returns(mockClientQueuesSection.Object);

            _monitor = new ConfigurationChangeMonitor(
                _mockLogger.Object,
                _mockConfiguration.Object,
                _settings,
                _mockNotificationService.Object);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ConfigurationChangeMonitor(
                null, _mockConfiguration.Object, _settings, _mockNotificationService.Object));
        }

        [Fact]
        public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ConfigurationChangeMonitor(
                _mockLogger.Object, null, _settings, _mockNotificationService.Object));
        }

        [Fact]
        public void Constructor_WithNullSettings_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ConfigurationChangeMonitor(
                _mockLogger.Object, _mockConfiguration.Object, null, _mockNotificationService.Object));
        }

        [Fact]
        public void Constructor_WithNullNotificationService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ConfigurationChangeMonitor(
                _mockLogger.Object, _mockConfiguration.Object, _settings, null));
        }

        [Fact]
        public async Task ExecuteAsync_WithDisabledMonitoring_ShouldReturnImmediately()
        {
            // Arrange
            var disabledSettings = new ConfigurationChangeMonitorSettings { Enabled = false };
            var monitor = new ConfigurationChangeMonitor(
                _mockLogger.Object,
                _mockConfiguration.Object,
                disabledSettings,
                _mockNotificationService.Object);

            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(500));

            // Act
            await monitor.StartAsync(cts.Token);
            await monitor.StopAsync(cts.Token);

            // Assert - Should complete quickly without timeout
            Assert.True(true);
        }

        [Fact]
        public void ConfigurationChangeMonitorSettings_DefaultValues_ShouldBeCorrect()
        {
            // Arrange & Act
            var settings = new ConfigurationChangeMonitorSettings();

            // Assert
            Assert.True(settings.Enabled);
            Assert.Equal(5000, settings.DebounceDelayMs);
        }

        [Fact]
        public async Task StopAsync_ShouldCompleteSuccessfully()
        {
            // Arrange
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));

            // Act
            await _monitor.StartAsync(cts.Token);
            await _monitor.StopAsync(CancellationToken.None);

            // Assert - Should complete without throwing
            Assert.True(true);
        }

        public void Dispose()
        {
            _monitor?.Dispose();
        }
    }

    /// <summary>
    /// Integration tests for ConfigurationChangeMonitor with real configuration
    /// </summary>
    public class ConfigurationChangeMonitorIntegrationTests : IDisposable
    {
        private readonly Mock<ILogger<ConfigurationChangeMonitor>> _mockLogger;
        private readonly Mock<IConfigurationChangeNotificationService> _mockNotificationService;
        private readonly ConfigurationBuilder _configBuilder;
        private readonly ConfigurationChangeMonitorSettings _settings;

        public ConfigurationChangeMonitorIntegrationTests()
        {
            _mockLogger = new Mock<ILogger<ConfigurationChangeMonitor>>();
            _mockNotificationService = new Mock<IConfigurationChangeNotificationService>();
            _configBuilder = new ConfigurationBuilder();
            _settings = new ConfigurationChangeMonitorSettings
            {
                Enabled = true,
                DebounceDelayMs = 50 // Very short for testing
            };
        }

        [Fact]
        public async Task Monitor_WithRealConfiguration_ShouldLoadInitialConfiguration()
        {
            // Arrange
            var initialConfig = new Dictionary<string, string>
            {
                ["ClientQueues:0:ClientCode"] = "TEST001",
                ["ClientQueues:0:QueueName"] = "test-queue",
                ["ClientQueues:0:QueueUrl"] = "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
                ["ClientQueues:0:Region"] = "us-east-1"
            };

            _configBuilder.AddInMemoryCollection(initialConfig);
            var configuration = _configBuilder.Build();

            var monitor = new ConfigurationChangeMonitor(
                _mockLogger.Object,
                configuration,
                _settings,
                _mockNotificationService.Object);

            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(200));

            // Act
            await monitor.StartAsync(cts.Token);
            await Task.Delay(100, cts.Token); // Allow time for initialization
            await monitor.StopAsync(CancellationToken.None);

            // Assert - Should complete without throwing
            Assert.True(true);
        }

        public void Dispose()
        {
            // No cleanup needed for this test
        }
    }
}
