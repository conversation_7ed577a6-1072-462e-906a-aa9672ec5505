namespace SQSLibrary.Configuration
{
    /// <summary>
    /// S3 configuration settings
    /// </summary>
    public class S3Settings
    {
        /// <summary>
        /// Timeout for S3 download operations in seconds
        /// </summary>
        public int DownloadTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Number of retry attempts for failed S3 downloads
        /// </summary>
        public int DownloadRetryAttempts { get; set; } = 3;
    }
}
