using System;
using System.Collections.Generic;
using Xunit;
using FluentAssertions;
using SQSLibrary.Services;
using SQSLibrary.Models;
using Newtonsoft.Json;

namespace SQSLibrary.Tests
{
    /// <summary>
    /// Unit tests for S3EventParser functionality
    /// </summary>
    public class S3EventParserTests
    {
        private readonly string _validS3EventJson = CreateValidS3EventJson();
        private readonly string _validSnsNotificationJson = CreateValidSnsNotificationJson();

        [Fact]
        public void ParseS3EventNotification_ValidS3Event_ShouldParseCorrectly()
        {
            // Act
            var result = S3EventParser.ParseS3EventNotification(_validS3EventJson, "test-message-001");

            // Assert
            result.Should().NotBeNull();
            result.Records.Should().HaveCount(1);
            
            var record = result.Records[0];
            record.EventSource.Should().Be("aws:s3");
            record.EventName.Should().Be("ObjectCreated:Put");
            record.AwsRegion.Should().Be("us-east-1");
            record.S3.Bucket.Name.Should().Be("test-bucket");
            record.S3.Object.Key.Should().Be("test-folder/test-file.xml");
            record.S3.Object.Size.Should().Be(1024);
        }

        [Fact]
        public void ParseS3EventNotification_ValidSnsNotification_ShouldExtractS3Event()
        {
            // Act
            var result = S3EventParser.ParseS3EventNotification(_validSnsNotificationJson, "test-message-002");

            // Assert
            result.Should().NotBeNull();
            result.Records.Should().HaveCount(1);
            
            var record = result.Records[0];
            record.EventSource.Should().Be("aws:s3");
            record.S3.Bucket.Name.Should().Be("test-bucket");
        }

        [Fact]
        public void ParseS3EventNotification_EmptyMessage_ShouldReturnNull()
        {
            // Act
            var result = S3EventParser.ParseS3EventNotification("", "test-message-003");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ParseS3EventNotification_NullMessage_ShouldReturnNull()
        {
            // Act
            var result = S3EventParser.ParseS3EventNotification(null, "test-message-004");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ParseS3EventNotification_InvalidJson_ShouldReturnNull()
        {
            // Arrange
            var invalidJson = "{ invalid json }";

            // Act
            var result = S3EventParser.ParseS3EventNotification(invalidJson, "test-message-005");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ParseS3EventNotification_NoRecords_ShouldReturnNull()
        {
            // Arrange
            var noRecordsJson = @"{ ""Records"": [] }";

            // Act
            var result = S3EventParser.ParseS3EventNotification(noRecordsJson, "test-message-006");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ParseS3EventNotification_NonS3Records_ShouldFilterToS3Only()
        {
            // Arrange
            var mixedRecordsJson = CreateMixedRecordsJson();

            // Act
            var result = S3EventParser.ParseS3EventNotification(mixedRecordsJson, "test-message-007");

            // Assert
            result.Should().NotBeNull();
            result.Records.Should().HaveCount(1);
            result.Records[0].EventSource.Should().Be("aws:s3");
        }

        [Fact]
        public void ExtractS3Objects_ValidS3Event_ShouldExtractCorrectly()
        {
            // Arrange
            var s3Event = JsonConvert.DeserializeObject<S3EventNotification>(_validS3EventJson);
            var receiptHandle = "test-receipt-handle";
            var messageId = "test-message-id";

            // Act
            var result = S3EventParser.ExtractS3Objects(s3Event, receiptHandle, messageId);

            // Assert
            result.Should().HaveCount(1);
            
            var s3Object = result[0];
            s3Object.BucketName.Should().Be("test-bucket");
            s3Object.ObjectKey.Should().Be("test-folder/test-file.xml");
            s3Object.Size.Should().Be(1024);
            s3Object.Region.Should().Be("us-east-1");
            s3Object.EventName.Should().Be("ObjectCreated:Put");
            s3Object.SqsReceiptHandle.Should().Be(receiptHandle);
            s3Object.SqsMessageId.Should().Be(messageId);
        }

        [Fact]
        public void ExtractS3Objects_NullS3Event_ShouldReturnEmptyList()
        {
            // Act
            var result = S3EventParser.ExtractS3Objects(null);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ExtractS3Objects_UrlEncodedObjectKey_ShouldDecodeCorrectly()
        {
            // Arrange
            var encodedKeyJson = CreateS3EventWithEncodedKey();
            var s3Event = JsonConvert.DeserializeObject<S3EventNotification>(encodedKeyJson);

            // Act
            var result = S3EventParser.ExtractS3Objects(s3Event);

            // Assert
            result.Should().HaveCount(1);
            result[0].ObjectKey.Should().Be("test folder/file with spaces.xml");
        }

        [Fact]
        public void ExtractS3Objects_MultipleRecords_ShouldExtractAll()
        {
            // Arrange
            var multiRecordJson = CreateMultipleS3RecordsJson();
            var s3Event = JsonConvert.DeserializeObject<S3EventNotification>(multiRecordJson);

            // Act
            var result = S3EventParser.ExtractS3Objects(s3Event);

            // Assert
            result.Should().HaveCount(3);
            result[0].ObjectKey.Should().Be("file1.xml");
            result[1].ObjectKey.Should().Be("file2.xml");
            result[2].ObjectKey.Should().Be("file3.xml");
        }

        [Fact]
        public void ParseS3EventNotification_MalformedJson_ShouldReturnNull()
        {
            // Arrange
            var malformedJson = @"{ ""Records"": [ { ""eventSource"": ""aws:s3"", ""s3"": { ""bucket"": { ""name"": ""test-bucket"" } } ] }"; // Missing closing brace

            // Act
            var result = S3EventParser.ParseS3EventNotification(malformedJson, "test-message");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ParseS3EventNotification_EmptyRecordsArray_ShouldReturnNull()
        {
            // Arrange
            var emptyRecordsJson = @"{ ""Records"": [] }";

            // Act
            var result = S3EventParser.ParseS3EventNotification(emptyRecordsJson, "test-message");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ParseS3EventNotification_MissingRecordsProperty_ShouldReturnNull()
        {
            // Arrange
            var noRecordsJson = @"{ ""Message"": ""Some other data"" }";

            // Act
            var result = S3EventParser.ParseS3EventNotification(noRecordsJson, "test-message");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void ExtractS3Objects_RecordsWithMissingS3Data_ShouldSkipInvalidRecords()
        {
            // Arrange
            var invalidRecordsJson = @"{
                ""Records"": [
                    {
                        ""eventSource"": ""aws:s3"",
                        ""eventName"": ""ObjectCreated:Put"",
                        ""awsRegion"": ""us-east-1""
                    },
                    {
                        ""eventSource"": ""aws:s3"",
                        ""eventName"": ""ObjectCreated:Put"",
                        ""awsRegion"": ""us-east-1"",
                        ""s3"": {
                            ""bucket"": { ""name"": ""test-bucket"" },
                            ""object"": { ""key"": ""valid-file.xml"", ""size"": 1024 }
                        }
                    }
                ]
            }";

            var s3Event = JsonConvert.DeserializeObject<S3EventNotification>(invalidRecordsJson);

            // Act
            var result = S3EventParser.ExtractS3Objects(s3Event);

            // Assert
            result.Should().HaveCount(1);
            result[0].ObjectKey.Should().Be("valid-file.xml");
        }

        [Fact]
        public void ParseS3EventNotification_EmptyEventSourceWithValidS3Data_ShouldParseCorrectly()
        {
            // Arrange - This simulates the dev environment data where eventSource is empty
            var devEnvironmentJson = @"{
                ""Records"": [
                    {
                        ""eventVersion"": """",
                        ""eventSource"": """",
                        ""awsRegion"": """",
                        ""eventTime"": ""0001-01-01T00:00:00Z"",
                        ""eventName"": """",
                        ""userIdentity"": {
                            ""principalId"": """"
                        },
                        ""requestParameters"": {
                            ""sourceIPAddress"": """"
                        },
                        ""responseElements"": null,
                        ""s3"": {
                            ""s3SchemaVersion"": ""1.0"",
                            ""configurationId"": ""tf-s3-topic-test"",
                            ""bucket"": {
                                ""name"": ""dev-washingtoncounty-mn-datalake-raw-01"",
                                ""ownerIdentity"": {
                                    ""principalId"": ""testid""
                                },
                                ""arn"": ""arn:aws:s3:::dev-washingtoncounty-mn-datalake-raw-01""
                            },
                            ""object"": {
                                ""key"": ""CHE/SOLACOM/2025/05/14/3__CI_TEST@WashingtonCountyMN_2025-05-14T19:56:29.789Z.xml"",
                                ""urlDecodedKey"": """",
                                ""versionId"": """",
                                ""eTag"": """",
                                ""sequencer"": """"
                            }
                        }
                    }
                ]
            }";

            // Act
            var result = S3EventParser.ParseS3EventNotification(devEnvironmentJson, "test-dev-message");

            // Assert
            result.Should().NotBeNull();
            result.Records.Should().HaveCount(1);

            var record = result.Records[0];
            record.S3.Bucket.Name.Should().Be("dev-washingtoncounty-mn-datalake-raw-01");
            record.S3.Object.Key.Should().Be("CHE/SOLACOM/2025/05/14/3__CI_TEST@WashingtonCountyMN_2025-05-14T19:56:29.789Z.xml");
        }

        [Fact]
        public void ExtractS3Objects_LargeObjectSizes_ShouldHandleCorrectly()
        {
            // Arrange
            var largeObjectJson = CreateS3EventWithLargeObject();
            var s3Event = JsonConvert.DeserializeObject<S3EventNotification>(largeObjectJson);

            // Act
            var result = S3EventParser.ExtractS3Objects(s3Event);

            // Assert
            result.Should().HaveCount(1);
            result[0].Size.Should().Be(1073741824); // 1GB
            result[0].ObjectKey.Should().Be("large-file.xml");
        }

        private static string CreateValidS3EventJson()
        {
            var s3Event = new S3EventNotification
            {
                Records = new List<S3EventRecord>
                {
                    new S3EventRecord
                    {
                        EventVersion = "2.1",
                        EventSource = "aws:s3",
                        AwsRegion = "us-east-1",
                        EventTime = DateTime.UtcNow,
                        EventName = "ObjectCreated:Put",
                        S3 = new S3Entity
                        {
                            Bucket = new S3Bucket { Name = "test-bucket" },
                            Object = new S3Object 
                            { 
                                Key = "test-folder/test-file.xml",
                                Size = 1024,
                                ETag = "test-etag"
                            }
                        }
                    }
                }
            };

            return JsonConvert.SerializeObject(s3Event);
        }

        private static string CreateValidSnsNotificationJson()
        {
            var snsNotification = new
            {
                Type = "Notification",
                MessageId = "test-sns-message-id",
                Message = CreateValidS3EventJson()
            };

            return JsonConvert.SerializeObject(snsNotification);
        }

        private static string CreateMixedRecordsJson()
        {
            var mixedEvent = new
            {
                Records = new object[]
                {
                    new { eventSource = "aws:sns", eventName = "SNS:Publish" },
                    new
                    {
                        eventSource = "aws:s3",
                        eventName = "ObjectCreated:Put",
                        awsRegion = "us-east-1",
                        s3 = new
                        {
                            bucket = new { name = "test-bucket" },
                            @object = new { key = "test-file.xml", size = 1024 }
                        }
                    }
                }
            };

            return JsonConvert.SerializeObject(mixedEvent);
        }

        private static string CreateS3EventWithEncodedKey()
        {
            var s3Event = new S3EventNotification
            {
                Records = new List<S3EventRecord>
                {
                    new S3EventRecord
                    {
                        EventSource = "aws:s3",
                        EventName = "ObjectCreated:Put",
                        AwsRegion = "us-east-1",
                        S3 = new S3Entity
                        {
                            Bucket = new S3Bucket { Name = "test-bucket" },
                            Object = new S3Object 
                            { 
                                Key = "test%20folder%2Ffile%20with%20spaces.xml",
                                Size = 1024
                            }
                        }
                    }
                }
            };

            return JsonConvert.SerializeObject(s3Event);
        }

        private static string CreateMultipleS3RecordsJson()
        {
            var s3Event = new S3EventNotification
            {
                Records = new List<S3EventRecord>()
            };

            for (int i = 1; i <= 3; i++)
            {
                s3Event.Records.Add(new S3EventRecord
                {
                    EventSource = "aws:s3",
                    EventName = "ObjectCreated:Put",
                    AwsRegion = "us-east-1",
                    S3 = new S3Entity
                    {
                        Bucket = new S3Bucket { Name = "test-bucket" },
                        Object = new S3Object
                        {
                            Key = $"file{i}.xml",
                            Size = 1024 * i
                        }
                    }
                });
            }

            return JsonConvert.SerializeObject(s3Event);
        }

        private static string CreateS3EventWithLargeObject()
        {
            var s3Event = new S3EventNotification
            {
                Records = new List<S3EventRecord>
                {
                    new S3EventRecord
                    {
                        EventSource = "aws:s3",
                        EventName = "ObjectCreated:Put",
                        AwsRegion = "us-east-1",
                        S3 = new S3Entity
                        {
                            Bucket = new S3Bucket { Name = "test-bucket" },
                            Object = new S3Object
                            {
                                Key = "large-file.xml",
                                Size = 1073741824 // 1GB
                            }
                        }
                    }
                }
            };

            return JsonConvert.SerializeObject(s3Event);
        }
    }
}
