# Git
.git
.gitignore
.gitattributes

# Documentation
docs/
README.md
*.md
LICENSE

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Environment files
.env*
**/.env

# Build artifacts
**/bin/
**/obj/
**/out/

# Visual Studio
.vs/
**/.vs
*.user
**/*.*proj.user
*.suo
*.cache
*.docstates
*.tmp
**/*.dbmdl
**/*.jfm

# Test results
TestResults/
[Tt]est[Rr]esult*/
*.trx
*.coverage
*.coveragexml

# Logs
logs/
*.log

# LocalStack data
localstack-data/

# Scripts
scripts/
*.bat
*.sh

# IDE files
.vscode/
**/.vscode
.idea/
**/.project
**/.settings
**/.toolstarget
**/.classpath

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# Package files
*.nupkg
*.snupkg

# Backup files
*.bak
*.backup