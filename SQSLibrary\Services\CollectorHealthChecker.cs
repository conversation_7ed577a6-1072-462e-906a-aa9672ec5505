using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Serilog;
using SQSLibrary.Logging;

namespace SQSLibrary.Services
{
    /// <summary>
    /// Service for monitoring the health of the Collector API
    /// Provides efficient, non-blocking health checks with circuit breaker pattern
    /// </summary>
    public class CollectorHealthChecker : IHealth<PERSON><PERSON><PERSON>, IDisposable
    {
        private static readonly ILogger _logger = LoggerFactory.ForContext<CollectorHealthChecker>();
        private readonly HttpClient _httpClient;
        private readonly string _healthCheckUrl;
        private readonly int _maxConsecutiveFailures;

        private volatile bool _isHealthy = true;
        private volatile int _consecutiveFailures = 0;
        private DateTime _lastHealthCheck = DateTime.MinValue;
        private DateTime _lastSuccessfulCheck = DateTime.UtcNow;
        private readonly object _timeLock = new object();

        private readonly Timer _healthCheckTimer;
        private readonly SemaphoreSlim _healthCheckSemaphore;
        private bool _disposed = false;

        public CollectorHealthChecker(
            string healthCheckUrl,
            TimeSpan? healthCheckInterval = null,
            TimeSpan? healthCheckTimeout = null,
            int maxConsecutiveFailures = 3,
            HttpClient httpClient = null,
            bool startTimer = true)
        {
            if (string.IsNullOrWhiteSpace(healthCheckUrl))
            {
                throw new ArgumentException("Health check URL cannot be null or empty", nameof(healthCheckUrl));
            }

            var interval = healthCheckInterval ?? TimeSpan.FromSeconds(30);
            var timeout = healthCheckTimeout ?? TimeSpan.FromSeconds(10);

            _healthCheckUrl = healthCheckUrl;
            _maxConsecutiveFailures = maxConsecutiveFailures;

            _httpClient = httpClient ?? new HttpClient
            {
                Timeout = timeout
            };

            _healthCheckSemaphore = new SemaphoreSlim(1, 1);

            // Start periodic health checks if requested
            if (startTimer)
            {
                _healthCheckTimer = new Timer(PerformHealthCheckCallback, null, TimeSpan.Zero, interval);
            }

            _logger.Information("CollectorHealthChecker initialized for {HealthCheckUrl} with {IntervalMs}ms interval, timer: {TimerEnabled}",
                _healthCheckUrl, interval.TotalMilliseconds, startTimer);
        }

        /// <summary>
        /// Gets the current health status of the collector API
        /// </summary>
        public bool IsHealthy => _isHealthy;

        /// <summary>
        /// Gets the number of consecutive failures
        /// </summary>
        public int ConsecutiveFailures => _consecutiveFailures;

        /// <summary>
        /// Gets the time of the last successful health check
        /// </summary>
        public DateTime LastSuccessfulCheck
        {
            get
            {
                lock (_timeLock)
                {
                    return _lastSuccessfulCheck;
                }
            }
        }

        /// <summary>
        /// Gets the time of the last health check attempt
        /// </summary>
        public DateTime LastHealthCheck
        {
            get
            {
                lock (_timeLock)
                {
                    return _lastHealthCheck;
                }
            }
        }

        /// <summary>
        /// Manually trigger a health check (non-blocking)
        /// </summary>
        /// <returns>Current health status</returns>
        public async Task<bool> CheckHealthAsync()
        {
            if (_disposed)
                return false;

            // Non-blocking check - if already checking, return current status
            if (!await _healthCheckSemaphore.WaitAsync(0))
            {
                _logger.Debug("Health check already in progress, returning current status: {IsHealthy}", _isHealthy);
                return _isHealthy;
            }

            try
            {
                return await PerformHealthCheckInternal();
            }
            finally
            {
                _healthCheckSemaphore.Release();
            }
        }

        /// <summary>
        /// Wait for the collector to become healthy (with timeout)
        /// </summary>
        /// <param name="timeout">Maximum time to wait</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if collector becomes healthy within timeout</returns>
        public async Task<bool> WaitForHealthyAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            var deadline = DateTime.UtcNow.Add(timeout);

            while (DateTime.UtcNow < deadline && !cancellationToken.IsCancellationRequested)
            {
                if (await CheckHealthAsync())
                {
                    return true;
                }

                // Calculate remaining time and wait appropriately
                var remainingTime = deadline - DateTime.UtcNow;
                var delayTime = TimeSpan.FromSeconds(Math.Min(5, remainingTime.TotalSeconds));

                if (delayTime <= TimeSpan.Zero)
                {
                    break;
                }

                try
                {
                    await Task.Delay(delayTime, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }

            return _isHealthy;
        }

        private async void PerformHealthCheckCallback(object state)
        {
            if (_disposed)
                return;

            try
            {
                if (await _healthCheckSemaphore.WaitAsync(0))
                {
                    try
                    {
                        await PerformHealthCheckInternal();
                    }
                    finally
                    {
                        if (!_disposed)
                        {
                            _healthCheckSemaphore.Release();
                        }
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                // Expected during disposal, ignore
            }
        }

        private async Task<bool> PerformHealthCheckInternal()
        {
            lock (_timeLock)
            {
                _lastHealthCheck = DateTime.UtcNow;
            }

            try
            {
                _logger.Debug("Performing health check on {HealthCheckUrl}", _healthCheckUrl);

                var response = await _httpClient.GetAsync(_healthCheckUrl);

                if (response.IsSuccessStatusCode)
                {
                    // Health check successful
                    var wasUnhealthy = !_isHealthy;
                    _isHealthy = true;
                    _consecutiveFailures = 0;
                    lock (_timeLock)
                    {
                        _lastSuccessfulCheck = DateTime.UtcNow;
                    }

                    if (wasUnhealthy)
                    {
                        _logger.Information("Collector API health restored. Service is now healthy at {HealthCheckUrl}", _healthCheckUrl);
                    }
                    else
                    {
                        _logger.Debug("Collector API health check successful");
                    }

                    return true;
                }
                else
                {
                    HandleHealthCheckFailure($"HTTP {response.StatusCode}: {response.ReasonPhrase}");
                    return false;
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                HandleHealthCheckFailure("Health check timeout");
                return false;
            }
            catch (HttpRequestException ex)
            {
                HandleHealthCheckFailure($"Network error: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                HandleHealthCheckFailure($"Unexpected error: {ex.Message}");
                return false;
            }
        }

        private void HandleHealthCheckFailure(string reason)
        {
            _consecutiveFailures++;

            if (_consecutiveFailures >= _maxConsecutiveFailures && _isHealthy)
            {
                _isHealthy = false;
                _logger.Warning("Collector API marked as unhealthy after {ConsecutiveFailures} consecutive failures. Last failure: {Reason}",
                    _consecutiveFailures, reason);
            }
            else
            {
                _logger.Debug("Health check failed ({ConsecutiveFailures}/{MaxFailures}): {Reason}",
                    _consecutiveFailures, _maxConsecutiveFailures, reason);
            }
        }



        public void Dispose()
        {
            if (_disposed)
                return;

            Dispose(true);
            _logger.Debug("CollectorHealthChecker disposed");
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _healthCheckTimer?.Dispose();
                _httpClient?.Dispose();
                _healthCheckSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
